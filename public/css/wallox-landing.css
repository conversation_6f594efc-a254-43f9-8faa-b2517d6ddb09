/*--------------------------------------------------------------
>>> TABLE OF CONTENTS:
----------------------------------------------------------------
# variables
# Utility
# Common
# Hero Section
# Feature Section
--------------------------------------------------------------*/
/*--------------------------------------------------------------
# variables
--------------------------------------------------------------*/
:root {
  --wallox-font: "Plus Jakarta Sans", sans-serif;
  --wallox-heading-font: "Plus Jakarta Sans", sans-serif;
  --wallox-special-font: "Outfit", sans-serif;
  --wallox-text: #7E7C76;
  --wallox-text-rgb: 126, 124, 118;
  --wallox-text-dark: #2E2A20;
  --wallox-text-dark-rgb: 46, 42, 32;
  --wallox-text-gray: #89868d;
  --wallox-text-gray-rgb: 137, 134, 141;
  --wallox-text-gray2: #D9D9D9;
  --wallox-text-gray2-rgb: 217, 217, 217;
  --wallox-base: #DF9E42;
  --wallox-base-rgb: 223, 158, 66;
  --wallox-gray: #F4EDE4;
  --wallox-gray-rgb: 244, 237, 228;
  --wallox-white: #fff;
  --wallox-white-rgb: 255, 255, 255;
  --wallox-border-color: #E4DACC;
  --wallox-border-color-rgb: 228, 218, 204;
  --wallox-letter-space: 0.1em;
  --wallox-letter-space-xl: 0.2em;
}

/*--------------------------------------------------------------
# Utility
--------------------------------------------------------------*/
.mt-20 {
  margin-top: 20px;
}

.mt-30 {
  margin-top: 30px;
}

.mt-40 {
  margin-top: 40px;
}

.mt-50 {
  margin-top: 50px;
}

.mt-60 {
  margin-top: 60px;
}

.mt-80 {
  margin-top: 80px;
}

.mt-120 {
  margin-top: 120px;
}

.mt--60 {
  margin-top: -60px;
}

.mt--120 {
  margin-top: -120px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mb-50 {
  margin-bottom: 50px;
}

.mb-60 {
  margin-bottom: 60px;
}

.mb-80 {
  margin-bottom: 80px;
}

.mb-120 {
  margin-bottom: 120px;
}

.mb--60 {
  margin-bottom: -60px;
}

.mb--120 {
  margin-bottom: -120px;
}

.pt-20 {
  padding-top: 20px;
}

.pt-30 {
  padding-top: 30px;
}

.pt-40 {
  padding-top: 40px;
}

.pt-50 {
  padding-top: 50px;
}

.pt-60 {
  padding-top: 60px;
}

.pt-80 {
  padding-top: 80px;
}

.pt-100 {
  padding-top: 100px;
}

.pt-110 {
  padding-top: 110px;
}

.pt-115 {
  padding-top: 115px;
}

.pt-120 {
  padding-top: 120px;
}

.pt-142 {
  padding-top: 142px;
}

.pb-20 {
  padding-bottom: 20px;
}

.pb-30 {
  padding-bottom: 30px;
}

.pb-40 {
  padding-bottom: 40px;
}

.pb-50 {
  padding-bottom: 50px;
}

.pb-60 {
  padding-bottom: 60px;
}

.pb-80 {
  padding-bottom: 80px;
}

.pb-90 {
  padding-bottom: 90px;
}

.pb-100 {
  padding-bottom: 100px;
}

.pb-110 {
  padding-bottom: 110px;
}

.pb-115 {
  padding-bottom: 115px;
}

.pb-120 {
  padding-bottom: 120px;
}

.pl-5 {
  padding-left: 5px;
}

.pl-10 {
  padding-left: 10px;
}

.pl-15 {
  padding-left: 15px;
}

.pl-20 {
  padding-left: 20px;
}

.pl-30 {
  padding-left: 30px;
}

.pr-5 {
  padding-right: 5px;
}

.pr-10 {
  padding-right: 10px;
}

.pr-15 {
  padding-right: 15px;
}

.pr-20 {
  padding-right: 20px;
}

.pr-30 {
  padding-right: 30px;
}

.wallox-btn {
  display: inline-block;
  vertical-align: middle;
  -webkit-appearance: none;
  border: none;
  outline: none !important;
  background-color: var(--wallox-text-dark, #2E2A20);
  color: #fff;
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  padding: 15px 24px;
  transition: 500ms;
  background-color: var(--wallox-text-dark, #2E2A20);
  color: #fff;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 1.25;
  text-transform: capitalize;
  border-radius: 100px;
  position: relative;
  overflow: hidden;
  text-align: center;
}
.wallox-btn:hover {
  color: var(--wallox-text-dark, #2E2A20);
  background-color: #fff;
}
.wallox-btn::before {
  content: "";
  background-color: var(--wallox-base, #DF9E42);
  border-radius: 100px;
  display: block;
  height: 100%;
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
  transform: translate(-100%, 0) rotate(10deg);
  transform-origin: top left;
  transition: 0.2s transform ease-out;
  will-change: transform;
  z-index: -1;
}
.wallox-btn--base {
  background: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
}
.wallox-btn--base::before {
  background-color: var(--wallox-text-dark, #2E2A20);
}
.wallox-btn--base:hover {
  color: var(--wallox-white, #fff);
}
.wallox-btn--border {
  background: transparent;
  border: 2px solid var(--wallox-text, #7E7C76);
}
.wallox-btn--border:hover {
  color: var(--wallox-white, #fff);
  border-color: transparent;
}
.wallox-btn:hover {
  color: var(--wallox-white, #fff);
  background: transparent;
  transform: scale(1.05);
  will-change: transform;
}
.wallox-btn:hover::before {
  transform: translate(0, 0);
}

/*--------------------------------------------------------------
# Common
--------------------------------------------------------------*/
body {
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  color: var(--wallox-text, #7E7C76);
  font-size: 16px;
  line-height: 2.125;
  font-weight: 500;
}

body.locked {
  overflow: hidden;
}

a {
  color: var(--wallox-base, #DF9E42);
  transition: all 400ms ease;
}

a,
a:hover,
a:focus,
a:visited {
  text-decoration: none;
}

::placeholder {
  color: inherit;
  opacity: 1;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--wallox-heading-font, "Plus Jakarta Sans", serif);
  color: var(--wallox-text-dark, #2E2A20);
}
@media (max-width: 575px) {
  h1 br,
  h2 br,
  h3 br,
  h4 br,
  h5 br,
  h6 br {
    display: none;
  }
}

@media (max-width: 575px) {
  p br {
    display: none;
  }
}

::placeholder {
  color: inherit;
  opacity: 1;
}

.page-wrapper {
  position: relative;
  margin: 0 auto;
  width: 100%;
  min-width: 300px;
  overflow: hidden;
}

.container-fluid,
.container {
  padding-left: 15px;
  padding-right: 15px;
}

@media (min-width: 1200px) {
  .container {
    max-width: 1200px;
  }
}
.row {
  --bs-gutter-x: 30px;
}

.gutter-y-10 {
  --bs-gutter-y: 10px;
}

.gutter-y-15 {
  --bs-gutter-y: 15px;
}

.gutter-y-20 {
  --bs-gutter-y: 20px;
}

.gutter-y-30 {
  --bs-gutter-y: 30px;
}

.gutter-y-60 {
  --bs-gutter-y: 60px;
}

.text-start {
  text-align: left !important;
}

.tabs-box .tabs-content .tab:not(.active-tab) {
  display: none;
}

.bootstrap-select .dropdown-menu {
  padding-top: 0;
  padding-bottom: 0;
  border-radius: 0;
}
.bootstrap-select .dropdown-item.active,
.bootstrap-select .dropdown-item:active {
  background-color: var(--wallox-base, #DF9E42);
}

/* tiny slider button customize */
.tns-outer .tns-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 40px;
}
.tns-outer .tns-controls button {
  width: 45px;
  height: 45px;
  border: 2px solid #f4f4f4;
  outline: none;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--wallox-text, #7E7C76);
  border-radius: 50%;
  margin-left: 5px;
  margin-right: 5px;
}

.block-title {
  margin-top: -8px;
  margin-bottom: 50px;
}
.block-title__decor {
  width: 21px;
  height: 14px;
  background-image: url(../images/shapes/leaf-1-1.png);
  background-repeat: no-repeat;
  background-position: top center;
  display: inline-block;
  line-height: 1;
  margin-bottom: -5px;
  position: relative;
  top: -7px;
}
.block-title p {
  margin: 0;
  color: var(--wallox-text, #7E7C76);
  font-size: 16px;
  line-height: 1;
  margin-bottom: 7px;
}
@media (min-width: 768px) {
  .block-title p {
    font-size: 18px;
  }
}
@media (min-width: 992px) {
  .block-title p {
    font-size: 20px;
  }
}
.block-title h3 {
  margin: 0;
  font-size: 35px;
  color: var(--wallox-text-dark, #2E2A20);
  font-family: var(--wallox-special-font, "Outfit", sans-serif);
}
@media (min-width: 768px) {
  .block-title h3 {
    font-size: 42px;
  }
}
@media (min-width: 992px) {
  .block-title h3 {
    font-size: 50px;
  }
}

.ul-list-one {
  margin-bottom: 0;
}
.ul-list-one li {
  position: relative;
  padding-left: 45px;
  font-size: 16px;
  font-weight: 500;
  color: var(--wallox-text-dark, #2E2A20);
}
@media (min-width: 481px) {
  .ul-list-one li {
    font-size: 20px;
  }
}
.ul-list-one li::before {
  content: "\e907";
  color: var(--wallox-base, #DF9E42);
  font-size: 26px;
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  font-family: "azino-icon";
}

.preloader {
  position: fixed;
  background-color: var(--wallox-text-dark, #2E2A20);
  background-position: center center;
  background-repeat: no-repeat;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9991;
  display: -webkit-box;
  display: flex;
  -webkit-box-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  align-items: center;
  text-align: center;
}
.preloader__image {
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation-name: flipInY;
  animation-name: flipInY;
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 60px auto;
  width: 100%;
  height: 100%;
}

/* scroll to top */
.scroll-to-top {
  display: flex;
  align-items: center;
  width: auto;
  height: 35px;
  background: transparent;
  position: fixed;
  bottom: 60px;
  right: -12px;
  z-index: 99;
  text-align: center;
  opacity: 0;
  visibility: hidden;
  transform: rotate(-90deg);
  cursor: pointer;
  transition: all 0.2s ease;
}
.scroll-to-top__text {
  display: inline;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-weight: 700;
  margin-right: 8px;
}
.scroll-to-top__wrapper {
  display: inline-block;
  width: 30px;
  height: 4px;
  background-color: var(--wallox-base, #DF9E42);
  position: relative;
  overflow: hidden;
}
.scroll-to-top__inner {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: var(--wallox-text-dark, #2E2A20);
}
.scroll-to-top.show {
  opacity: 1;
  visibility: visible;
  bottom: 70px;
}

/* post paginations */
.post-pagination {
  margin: 0;
  padding: 0;
  list-style: none;
  margin-top: 50px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 11px;
}
.post-pagination li:last-child a:hover, .post-pagination li:first-child a:hover {
  background: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
}
.post-pagination a {
  width: 40px;
  height: 40px;
  background-color: var(--wallox-gray, #F4EDE4);
  color: var(--wallox-text, #7E7C76);
  text-transform: capitalize;
  text-align: center;
  border-radius: 50%;
  border: 1px solid transparent;
  transition: all 400ms ease;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.post-pagination a:hover {
  background: transparent;
  color: var(--wallox-base, #DF9E42);
  border: 1px solid var(--wallox-base, #DF9E42);
}
.post-pagination a i {
  line-height: 1;
  font-size: 14px;
}

/* Owl Shadow */
.wallox-owl__carousel--with-shadow .owl-stage-outer {
  overflow: visible;
}
.wallox-owl__carousel--with-shadow .owl-item {
  opacity: 0;
  visibility: hidden;
  transition: opacity 500ms ease, visibility 500ms ease;
}
.wallox-owl__carousel--with-shadow .owl-item.active {
  opacity: 1;
  visibility: visible;
}

/* Owl Nav */
.wallox-owl__carousel--basic-nav.owl-carousel .owl-nav {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 60px;
}
.wallox-owl__carousel--basic-nav.owl-carousel .owl-nav button {
  border: none;
  outline: none;
  border-radius: 50%;
  margin: 0;
  padding: 0;
}
.wallox-owl__carousel--basic-nav.owl-carousel .owl-nav button span {
  border: none;
  outline: none;
  width: 50px;
  height: 50px;
  background-color: var(--wallox-gray, #F4EDE4);
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--wallox-text, #7E7C76);
  border-radius: 50%;
  font-size: 14px;
  color: var(--wallox-text, #7E7C76);
  transition: all 500ms ease;
}
.wallox-owl__carousel--basic-nav.owl-carousel .owl-nav button span:hover {
  background-color: var(--wallox-text-dark, #2E2A20);
  color: var(--wallox-white, #fff);
}
.wallox-owl__carousel--basic-nav.owl-carousel .owl-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-top: 60px;
}
.wallox-owl__carousel--basic-nav.owl-carousel .owl-dots .owl-dot span {
  background-color: var(--wallox-gray2, #D9D9D9);
  width: 10px;
  border-radius: 50%;
  height: 10px;
  margin: 0;
}
.wallox-owl__carousel--basic-nav.owl-carousel .owl-dots .owl-dot:hover span, .wallox-owl__carousel--basic-nav.owl-carousel .owl-dots .owl-dot.active span {
  background-color: var(--wallox-base, #DF9E42);
}
.wallox-owl__carousel--basic-nav.owl-carousel .owl-nav.disabled + .owl-dots {
  margin-top: 60px;
}

.wallox-owl__carousel__counter {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: -80%;
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
  gap: 19px;
}
@media (max-width: 1199px) {
  .wallox-owl__carousel__counter {
    left: -40%;
  }
}
@media (max-width: 991px) {
  .wallox-owl__carousel__counter {
    left: -20%;
  }
}
@media (max-width: 767px) {
  .wallox-owl__carousel__counter {
    display: none;
  }
}
.wallox-owl__carousel__counter .wallox-owl__carousel__counter__total {
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  font-style: normal;
  font-weight: 600;
  font-size: 24px;
  line-height: 100%;
  color: var(--wallox-text-dark, #2E2A20);
  text-transform: uppercase;
  position: relative;
}
.wallox-owl__carousel__counter .wallox-owl__carousel__counter__total::before {
  content: "";
  position: absolute;
  top: calc(50% + 1px);
  left: -10px;
  transform: translateY(-50%) rotate(20deg);
  width: 1px;
  height: 16px;
  background-color: var(--wallox-border-color, #E4DACC);
}
.wallox-owl__carousel__counter .wallox-owl__carousel__counter__current {
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  font-style: normal;
  font-weight: 800;
  font-size: 24px;
  line-height: 67%;
  position: relative;
  color: var(--wallox-base, #DF9E42);
  text-transform: uppercase;
}

/* Section Title */
.sec-title {
  padding-bottom: 55px;
}
@media (max-width: 767px) {
  .sec-title {
    padding-bottom: 35px;
  }
}
@media (max-width: 575px) {
  .sec-title {
    padding-bottom: 30px;
  }
}
.sec-title__image {
  margin-right: 10px;
}
.sec-title__tagline {
  margin: 0;
  color: var(--wallox-base, #DF9E42);
  letter-spacing: 0.15em;
  text-transform: uppercase;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 1.25;
}
.sec-title__title {
  margin-top: 11px;
  color: var(--wallox-text-dark, #2E2A20);
  font-style: normal;
  font-weight: 800;
  font-size: 45px;
  line-height: 1.2666666667;
  letter-spacing: -0.02em;
  margin-bottom: 0;
  padding-bottom: 0;
}
@media (max-width: 767px) {
  .sec-title__title {
    font-size: 40px;
  }
}
@media (max-width: 575px) {
  .sec-title__title {
    font-size: 35px;
  }
}

.wallox-slick__carousel--with-shadow .slick-list {
  overflow: visible;
}
.wallox-slick__carousel--with-shadow .slick-slide {
  opacity: 0;
  visibility: hidden;
  transition: opacity 500ms ease, visibility 500ms ease;
}
.wallox-slick__carousel--with-shadow .slick-slide.slick-active {
  opacity: 1;
  visibility: visible;
}

.ui-datepicker .ui-datepicker-header {
  background-image: none;
  background-color: var(--wallox-text-dark, #2E2A20);
  color: var(--wallox-white, #fff);
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
}

.ui-datepicker-calendar th span {
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
}
.ui-datepicker-calendar td {
  background-color: var(--wallox-gray, #F4EDE4);
  background-image: none;
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  color: var(--wallox-text, #7E7C76);
}
.ui-datepicker-calendar td a {
  border-color: var(--wallox-border-color, #E4DACC);
  background-color: var(--wallox-gray, #F4EDE4);
  background-image: none;
}
.ui-datepicker-calendar .ui-state-default,
.ui-datepicker-calendar .ui-widget-content .ui-state-default,
.ui-datepicker-calendar .ui-widget-header .ui-state-default {
  border-color: var(--wallox-border-color, #E4DACC);
  background-color: var(--wallox-gray, #F4EDE4);
  background-image: none;
  color: var(--wallox-text, #7E7C76);
  padding: 10px 5px;
  text-align: center;
  line-height: 1em;
}
.ui-datepicker-calendar .ui-state-default:hover,
.ui-datepicker-calendar .ui-widget-content .ui-state-default:hover,
.ui-datepicker-calendar .ui-widget-header .ui-state-default:hover {
  color: var(--wallox-white, #fff);
  background-color: var(--wallox-base, #DF9E42);
}
.ui-datepicker-calendar .ui-state-highlight,
.ui-datepicker-calendar .ui-widget-content .ui-state-highlight,
.ui-datepicker-calendar .ui-widget-header .ui-state-highlight {
  color: var(--wallox-white, #fff);
  background-color: var(--wallox-base, #DF9E42);
}

.ui-datepicker .ui-datepicker-prev,
.ui-datepicker .ui-datepicker-next {
  background-image: none;
  background-color: var(--wallox-white, #fff);
  color: var(--wallox-text-dark, #2E2A20);
}
.ui-datepicker .ui-datepicker-prev:hover,
.ui-datepicker .ui-datepicker-next:hover {
  background-color: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
  top: 2px;
}

.ui-datepicker .ui-datepicker-prev:hover {
  left: 2px;
}

.ui-datepicker .ui-datepicker-next:hover {
  right: 2px;
}

/*--------------------------------------------------------------
# Hero Section
--------------------------------------------------------------*/
.hero-section {
  background-color: var(--wallox-base, #DF9E42);
  position: relative;
  padding-top: 80px;
  padding-bottom: 80px;
  z-index: 1;
}
@media (min-width: 1200px) {
  .hero-section {
    padding-bottom: 162px;
  }
}
.hero-section__bg {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
  left: 0;
  z-index: -1;
}
.hero-section .container {
  position: relative;
}
.hero-section__logo {
  image-rendering: -moz-crisp-edges;
  image-rendering: -o-crisp-edges;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  -ms-interpolation-mode: nearest-neighbor;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}
.hero-section__content {
  text-align: center;
}
@media (min-width: 992px) {
  .hero-section__content {
    text-align: left;
  }
}
.hero-section__title {
  margin: 0;
  color: var(--wallox-white, #fff);
  margin-top: 80px;
  margin-bottom: 27px;
  font-family: var(--wallox-special-font, "Outfit", sans-serif);
  font-style: normal;
  font-weight: 700;
  font-size: 65px;
  line-height: 108%;
  text-transform: capitalize;
}
@media (max-width: 991px) {
  .hero-section__title {
    margin-top: 50px;
  }
}
@media (min-width: 1200px) {
  .hero-section__title {
    margin-top: 150px;
  }
}
.hero-section__text {
  margin: 0 0 30px;
  color: var(--wallox-white, #fff);
  font-family: var(--wallox-special-font, "Outfit", sans-serif);
  font-style: normal;
  font-weight: 600;
  font-size: 20px;
  line-height: 180%;
}
@media (min-width: 1200px) {
  .hero-section__text {
    margin-bottom: 51px;
    font-size: 20px;
  }
}
.hero-section__text br {
  display: none;
}
@media (min-width: 768px) {
  .hero-section__text br {
    display: inherit;
  }
}
.hero-section .wallox-btn {
  background-color: var(--wallox-white, #fff);
  color: var(--wallox-base, #DF9E42);
  display: inline-flex;
  border-radius: 6px;
  align-items: center;
  transform: scale(1);
  border: 1px solid transparent;
  text-transform: uppercase;
  padding-left: 17px;
  padding-right: 17px;
}
.hero-section .wallox-btn i {
  margin-left: 24px;
  font-size: 14px;
}
.hero-section .wallox-btn::before {
  border-radius: 6px;
}
.hero-section .wallox-btn:hover {
  color: var(--wallox-white, #fff);
  border: 1px solid var(--wallox-white, #fff);
  transform: scale(1);
}
.hero-section__images {
  display: none;
}
.hero-section__images img {
  position: absolute;
  bottom: 0;
  right: 0;
  box-shadow: -5px 0px 40px 0px rgba(0, 0, 0, 0.15);
}
@media (min-width: 1200px) {
  .hero-section__images {
    display: block;
  }
}
.hero-section__arrow {
  width: 180px;
  height: 84px;
  background-image: url(../images/wallox-landing/hero-arrow-1.png);
  background-repeat: no-repeat;
  position: absolute;
  bottom: 188px;
  left: 39%;
  -webkit-animation-name: float-bob-y-2;
  animation-name: float-bob-y-2;
  -webkit-animation-duration: 4s;
  animation-duration: 4s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
  display: none;
}
@media (min-width: 1200px) {
  .hero-section__arrow {
    display: block;
  }
}
.hero-section__images {
  display: none;
}
.hero-section__images img {
  position: absolute;
  bottom: 0;
  right: 0;
  box-shadow: -5px 0px 40px 0px rgba(0, 0, 0, 0.15);
}
@media (min-width: 1200px) {
  .hero-section__images {
    display: block;
  }
}
.hero-section__arrow {
  width: 180px;
  height: 84px;
  background-image: url(../images/wallox-landing/hero-arrow-1.png);
  background-repeat: no-repeat;
  position: absolute;
  bottom: 188px;
  left: 35%;
  -webkit-animation-name: float-bob-y-2;
  animation-name: float-bob-y-2;
  -webkit-animation-duration: 4s;
  animation-duration: 4s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
  display: none;
}
@media (min-width: 1200px) {
  .hero-section__arrow {
    display: block;
  }
}
.hero-section__badge {
  width: 131px;
  height: 131px;
  background-image: url(../images/wallox-landing/hero-badge-1.png);
  background-repeat: no-repeat;
  position: absolute;
  top: 120px;
  left: 47%;
  -webkit-animation: sunMove 10s linear infinite;
  animation: sunMove 10s linear infinite;
  display: none;
}
@media (min-width: 1200px) {
  .hero-section__badge {
    display: block;
  }
}
.hero-section__cloth {
  position: absolute;
  bottom: 120px;
  left: 0;
  animation: topToBottom 3s ease-in-out infinite;
}

@-webkit-keyframes sunMove {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  25% {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
  }
  50% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
  }
  75% {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
  }
  100% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
}
@keyframes sunMove {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  25% {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
  }
  50% {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
  }
  75% {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
  }
  100% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
}
@-webkit-keyframes float-bob-y-2 {
  0% {
    -webkit-transform: translateY(0px) translateX(0px) rotate(0deg);
    transform: translateY(0px) translateX(0px) rotate(0deg);
  }
  50% {
    -webkit-transform: translateY(10px) translateX(10px) rotate(5deg);
    transform: translateY(10px) translateX(10px) rotate(5deg);
  }
  100% {
    -webkit-transform: translateY(0px) translateX(0px) rotate(0deg);
    transform: translateY(0px) translateX(0px) rotate(0deg);
  }
}
@keyframes float-bob-y-2 {
  0% {
    -webkit-transform: translateY(0px) translateX(0px) rotate(0deg);
    transform: translateY(0px) translateX(0px) rotate(0deg);
  }
  50% {
    -webkit-transform: translateY(10px) translateX(10px) rotate(5deg);
    transform: translateY(10px) translateX(10px) rotate(5deg);
  }
  100% {
    -webkit-transform: translateY(0px) translateX(0px) rotate(0deg);
    transform: translateY(0px) translateX(0px) rotate(0deg);
  }
}
@keyframes topToBottom {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(16px);
  }
  100% {
    transform: translateY(0px);
  }
}
/*--------------------------------------------------------------
# Feature Section
--------------------------------------------------------------*/
.features-two {
  background-color: var(--wallox-gray, #F4EDE4);
  padding-top: 80px;
  padding-bottom: 50px;
}
@media (max-width: 991px) {
  .features-two {
    padding-bottom: 50px;
  }
}
.features-two__box {
  text-align: center;
  position: relative;
}
@media (max-width: 991px) {
  .features-two__box {
    margin-bottom: 30px;
  }
}
.features-two__box img {
  max-height: 52px;
  display: inline-block;
  transition: all 500ms linear;
  transition-delay: 0s;
  transition-delay: 0s;
  transform: scale(1);
}
.features-two__box:hover img {
  transform: scale(0.9);
}
.features-two__title {
  margin: 22px 0 0;
  color: var(--wallox-text-dark, #2E2A20);
  font-family: var(--wallox-special-font, "Outfit", sans-serif);
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 180%;
  text-align: center;
  text-transform: capitalize;
}

/*--------------------------------------------------------------
# Demo Section
--------------------------------------------------------------*/
.demos-one {
  position: relative;
  padding: 120px 0 90px;
}
@media (max-width: 767px) {
  .demos-one {
    padding: 80px 0 50px;
  }
}
.demos-one__single {
  margin: 0 0 30px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  position: relative;
  text-align: center;
  background-color: var(--wallox-white, #fff);
  box-shadow: 0px 10px 60px 0px rgba(var(--wallox-text-dark-rgb, 46, 42, 32), 0.1);
}
.demos-one__single:hover {
  transform: translateY(-10px);
}
.demos-one__single:hover .demos-one__buttons {
  transform: scale(1, 1);
  opacity: 1;
  transform-origin: top center;
}
.demos-one__single:hover .demos-one__image__hover {
  transform: translateY(0);
}
.demos-one__single:hover .demos-one__image img {
  filter: blur(2px);
}
.demos-one__image {
  overflow: hidden;
  position: relative;
}
.demos-one__image img {
  max-width: 100%;
  transition: filter 500ms ease;
  filter: blur(0px);
}
.demos-one__image__hover {
  background-color: rgba(var(--wallox-text-dark-rgb, 46, 42, 32), 0.7);
  height: 100%;
  top: 0;
  position: absolute;
  transform: translateY(-100%);
  transition: all 0.5s ease 0s;
  width: 20%;
}
.demos-one__image__hover--1 {
  left: 0;
  transition-delay: 0.105s;
}
.demos-one__image__hover--2 {
  left: 20%;
  transition-delay: 0.15s;
}
.demos-one__image__hover--3 {
  left: 40%;
  transition-delay: 0.2s;
}
.demos-one__image__hover--4 {
  left: 60%;
  transition-delay: 0.25s;
}
.demos-one__image__hover--5 {
  left: 80%;
  transition-delay: 0.3s;
}
.demos-one__buttons {
  background-color: transparent;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  transform: scale(1, 1);
  transition: transform 500ms ease, opacity 600ms linear;
  transition-delay: 300ms;
  opacity: 0;
  z-index: 1;
}
.demos-one__buttons .wallox-btn {
  font-size: 14px;
  padding: 13px 30px;
  background-color: var(--wallox-text-dark, #2E2A20);
  display: inline-flex;
  border-radius: 10px;
}
.demos-one__buttons .wallox-btn::before {
  background-color: var(--wallox-base, #DF9E42);
  border-radius: 10px;
}
.demos-one__buttons .wallox-btn:hover {
  color: var(--wallox-white, #fff);
}
.demos-one__text {
  text-align: center;
  padding-top: 25px;
  padding-bottom: 27px;
}
.demos-one__text__title {
  margin: 0;
  font-family: var(--wallox-special-font, "Outfit", sans-serif);
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 180%;
}

/*--------------------------------------------------------------
#  Feature Section 02
--------------------------------------------------------------*/
.features-one {
  background-color: var(--wallox-gray, #F4EDE4);
  padding: 85px 0 70px;
}
@media (max-width: 767px) {
  .features-one {
    padding: 65px 0 30px;
  }
}
.features-one__title {
  margin: 0 0 50px;
  color: var(--wallox-text-dark, #2E2A20);
  font-family: var(--wallox-special-font, "Outfit", sans-serif);
  font-style: normal;
  font-weight: 700;
  font-size: 50px;
  line-height: 72%;
  text-transform: capitalize;
}
@media (max-width: 767px) {
  .features-one__title {
    font-size: 35px;
    margin-bottom: 30px;
  }
}
.features-one__grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
}
@media (max-width: 1199px) {
  .features-one__grid {
    grid-template-columns: repeat(5, 1fr);
  }
}
@media (max-width: 991px) {
  .features-one__grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
@media (max-width: 767px) {
  .features-one__grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
.features-one__box {
  text-align: center;
  position: relative;
  margin: 0 0 36px;
}
@media (max-width: 767px) {
  .features-one__box {
    margin-left: auto;
    margin-right: auto;
  }
}
.features-one__box__image {
  width: 131px;
  height: 135px;
  background-color: var(--wallox-white, #fff);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}
.features-one__box__image img {
  max-height: 121px;
  display: inline-block;
  transition: all 500ms linear;
  transition-delay: 0s;
  transition-delay: 0s;
  transform: scale(1);
}
.features-one__box__title {
  max-width: 131px;
  color: var(--wallox-text-dark, #2E2A20);
  margin: 18px 0 0;
  font-family: var(--wallox-special-font, "Outfit", sans-serif);
  font-style: normal;
  font-weight: 700;
  font-size: 15px;
  line-height: 19px;
  text-align: center;
  text-transform: capitalize;
}
.features-one__box:hover .features-one__box__image img {
  transform: scale(0.9);
}

/*--------------------------------------------------------------
#  Footer Section
--------------------------------------------------------------*/
.main-footer {
  background-color: var(--wallox-text-dark, #2E2A20);
  position: relative;
  padding-top: 120px;
  background-repeat: no-repeat;
  background-size: cover;
}
@media (max-width: 991px) {
  .main-footer {
    padding-top: 100px;
  }
}
@media (max-width: 767px) {
  .main-footer {
    padding-top: 70px;
  }
}
.main-footer .container {
  position: relative;
  z-index: 2;
}
.main-footer__title {
  margin: 0 0 30px;
  color: var(--wallox-white, #fff);
  font-style: normal;
  font-weight: 700;
  font-size: 35px;
  line-height: 80%;
  text-align: center;
  text-transform: capitalize;
}
@media (min-width: 1200px) {
  .main-footer__title {
    font-size: 45px;
  }
}
.main-footer__text {
  margin: 0 0 26px;
  color: var(--wallox-white, #fff);
  font-style: normal;
  font-weight: 500;
  font-size: 20px;
  line-height: 100%;
}
@media (min-width: 1200px) {
  .main-footer__text {
    font-size: 26px;
    margin: 0 0 36px;
  }
}
.main-footer .wallox-btn {
  background-color: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
  display: inline-flex;
  border-radius: 6px;
  align-items: center;
  gap: 19px;
  transform: scale(1);
  border: 1px solid transparent;
  text-transform: uppercase;
  padding-left: 17px;
  padding-right: 17px;
}
.main-footer .wallox-btn i {
  font-size: 14px;
}
.main-footer .wallox-btn::before {
  border-radius: 6px;
}
.main-footer .wallox-btn .wallox-btn__icon {
  position: relative;
}
.main-footer .wallox-btn .wallox-btn__icon::before {
  content: "";
  width: 1px;
  height: 100%;
  position: absolute;
  top: 50%;
  left: -9px;
  transform: translateY(-50%);
  background-color: rgba(var(--wallox-white-rgb, 255, 255, 255), 0.3);
  transition: all 400ms ease;
}
.main-footer .wallox-btn:hover {
  color: var(--wallox-white, #fff);
  border: 1px solid var(--wallox-white, #fff);
  transform: scale(1);
}
.main-footer__copyright {
  position: relative;
  border-top: 1px solid rgba(var(--wallox-white-rgb, 255, 255, 255), 0.1);
  margin: 99px 0 0;
  padding: 31px 0 34px;
}
@media (max-width: 767px) {
  .main-footer__copyright {
    margin-top: 50px;
    padding: 21px 0 24px;
  }
}
.main-footer__copyright p {
  color: var(--wallox-white, #fff);
  margin: 0;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 100%;
  text-transform: capitalize;
}
/*# sourceMappingURL=wallox-landing.css.map */
