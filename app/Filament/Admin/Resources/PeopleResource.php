<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\PeopleResource\Pages;
use App\Filament\Admin\Resources\PeopleResource\RelationManagers;
use App\Models\People;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Enums\PostStatus;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Support\Facades\Storage;
use Filament\Tables\Columns\ImageColumn;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Select;
use Guava\FilamentIconSelectColumn\Tables\Columns\IconSelectColumn;

class PeopleResource extends Resource
{
    protected static ?string $model = People::class;
    protected static ?string $navigationIcon = 'heroicon-o-user-group';
    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('full_name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('designation')
                    ->maxLength(255),
                FileUpload::make('image_url')
                    ->required()
                    ->label("Attorney's Picture")
                    ->image()
                    ->directory('people')
                    ->imageResizeMode('cover')
                    ->imageEditor()
                    ->imageEditorAspectRatios([
                        '16:9',
                        '4:3',
                        '1:1',
                    ])
                    ->imageResizeTargetWidth(250)
                    ->imageResizeTargetHeight(270),

               Forms\Components\TextInput::make('image_alt')
                    ->maxLength(200),

                 Select::make('status')
                    ->required()
                    ->options(PostStatus::class)
                    ->reactive(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                 ImageColumn::make('image_url')
                ->label('Image')
                ->size(50)
                ->circular()
                ->visibility('public'),
                Tables\Columns\TextColumn::make('full_name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('designation')
                    ->searchable(),
               IconSelectColumn::make('status')
                ->closeOnSelection()
                ->options(PostStatus::class)
            ])
            ->recordUrl(null)
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->after(function(People $people) {
                    if ($people->image_url) {
                        Storage::disk('public')->delete($people->image_url);
                     }
                }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPeople::route('/'),
            'create' => Pages\CreatePeople::route('/create'),
            'edit' => Pages\EditPeople::route('/{record}/edit'),
        ];
    }
}
