<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\WhyChooseUsResource\Pages;
use App\Filament\Admin\Resources\WhyChooseUsResource\RelationManagers;
use App\Models\WhyChooseUs;
use Filament\Forms;
use Filament\Forms\Form;
use Illuminate\Support\Str;
use Filament\Resources\Resource;
use Filament\Forms\Components\Select;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\RichEditor;
use App\Enums\PostStatus;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Support\Facades\Storage;
use Filament\Tables\Columns\ImageColumn;
use Filament\Forms\Components\FileUpload;
use Guava\FilamentIconSelectColumn\Tables\Columns\IconSelectColumn;

class WhyChooseUsResource extends Resource
{
    protected static ?string $model = WhyChooseUs::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-circle';
    protected static ?string $navigationGroup = 'About Us';
    protected static ?string $navigationLabel = 'Why Choose Us';
    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                 Forms\Components\TextInput::make('header')
                    ->required()
                    ->maxLength(150),
                Forms\Components\TextInput::make('subheader')
                    ->maxLength(50),

                RichEditor::make('content')
                ->required()
                ->toolbarButtons([
                    'attachFiles',
                    'blockquote',
                    'bold',
                    'bulletList',
                    'codeBlock',
                    'h1',
                    'h2',
                    'h3',
                    'italic',
                    'link',
                    'orderedList',
                    'redo',
                    'strike',
                    'underline',
                    'undo',
                ])
                ->columnSpanFull()
                ->extraInputAttributes(['style' => 'min-height: 30rem; max-height: 50vh; overflow-y: auto;']),

                FileUpload::make('image_url')
                    ->required()
                    ->label('Why Choose Us Image')
                    ->image()
                    ->directory('about')
                    ->imageResizeMode('cover')
                    ->imageEditor()
                    ->imageEditorAspectRatios([
                        '16:9',
                        '4:3',
                        '1:1',
                    ]),

                Forms\Components\TextInput::make('image_alt')
                    ->maxLength(200),

                Select::make('status')
                    ->required()
                    ->options(PostStatus::class)
                    ->reactive(),

                 Forms\Components\Fieldset::make('SEO')
                ->columnSpan('full')
                ->columns(['default' => 1])
                ->schema([
                     Forms\Components\TextInput::make('page_title')
                        ->label('Page Title')
                        ->columnSpanFull(),
                    Forms\Components\Textarea::make('page_description')
                    ->label('Meta Description')
                        ->columnSpanFull(),
                    Forms\Components\Textarea::make('page_keywords')
                    ->label('Meta Keywords')
                        ->columnSpanFull(),
                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                  ImageColumn::make('image_url')
                ->label('Image')
                ->size(50)
                ->circular()
                ->visibility('public'),
                TextColumn::make('header')
                ->words(5)
                ->searchable(),
                TextColumn::make('content')
                ->html()
                ->words(10)
                ->wrap(),

                IconSelectColumn::make('status')
                ->closeOnSelection()
                ->options(PostStatus::class)

            ])
            ->recordUrl(null)
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->after(function(WhyChooseUs $whyChooseUs) {
                    if ($whyChooseUs->image_url) {
                        Storage::disk('public')->delete($whyChooseUs->image_url);
                     }
                }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWhyChooseUs::route('/'),
            'create' => Pages\CreateWhyChooseUs::route('/create'),
            'edit' => Pages\EditWhyChooseUs::route('/{record}/edit'),
        ];
    }
}
