<?php

namespace App\Http\Controllers;

use App\Models\About;
use App\Models\People;
use App\Models\WhyChooseUs;
use Illuminate\Http\Request;

class AboutController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $about = About::select('*')->where('status', 'published')->first();
        $people = People::select('full_name', 'image_url','image_alt', 'designation')->where('status', 'published')->get();
        return view('about.whoweare')->with([
            'about' => $about,
            'people' => $people
        ]);
    }


     public function chooseUs()
    {
        $whychoose = WhyChooseUs::select('*')->where('status', 'published')->first();
        // dd($whychoose);
        $people = People::select('full_name', 'image_url','image_alt', 'designation')->where('status', 'published')->get();
        return view('about.whychooseus')->with([
            'whychoose' => $whychoose,
            'people' => $people
        ]);
    }


}