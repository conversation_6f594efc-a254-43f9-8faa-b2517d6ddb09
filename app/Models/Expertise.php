<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Expertise extends Model
{
    use HasFactory;

    protected $fillable = [
        'page_title',
        'page_description',
        'page_keywords',
        'slug',
        'icon',
        'title',
        'content',
        'image_url',
        'image_alt',
        'status'
    ];


    protected static function boot()
    {
        parent::boot();

        /** @var Model $model */
        static::updating(function ($model) {
            if ($model->isDirty('image_url') && ($model->getOriginal('image_url') !== null)) {
                Storage::disk('public')->delete($model->getOriginal('image_url'));
            }
        });
    }
}
