<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\ExpertiseResource\Pages;
use App\Filament\Admin\Resources\ExpertiseResource\RelationManagers;
use App\Models\Expertise;
use App\Enums\Icons;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Select;
use App\Enums\PostStatus;
use Illuminate\Support\Str;
use Filament\Forms\Components\FileUpload;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Support\Facades\Storage;
use Filament\Tables\Columns\ImageColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Guava\FilamentIconSelectColumn\Tables\Columns\IconSelectColumn;

class ExpertiseResource extends Resource
{
    protected static ?string $model = Expertise::class;
    protected static ?string $navigationIcon = 'heroicon-o-scale';
    protected static ?string $navigationLabel = 'Expertise';
    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->label('Title')
                    ->required()
                    ->maxLength(150)
                    ->columnSpanFull()
                    ->unique(ignoreRecord: true)
                    ->live(onBlur: true)
                    ->afterStateUpdated(fn (string $state, callable $set) => $set('slug', Str::slug($state))),

                Forms\Components\TextInput::make('slug')
                    ->required()
                    ->maxLength(200)
                    ->readOnly()
                    ->columnSpanFull(),

                Select::make('icon')
                    ->required()
                    ->enum(Icons::class)
                    ->options(Icons::class),

                Select::make('status')
                    ->required()
                    ->options(PostStatus::class)
                    ->reactive(),

                RichEditor::make('content')
                ->required()
                ->toolbarButtons([
                    'attachFiles',
                    'blockquote',
                    'bold',
                    'bulletList',
                    'codeBlock',
                    'h1',
                    'h2',
                    'h3',
                    'italic',
                    'link',
                    'orderedList',
                    'redo',
                    'strike',
                    'underline',
                    'undo',
                ])
                ->columnSpanFull()
                ->extraInputAttributes(['style' => 'min-height: 30rem; max-height: 50vh; overflow-y: auto;']),

                FileUpload::make('image_url')
                    ->required()
                    ->label('Expertise Image')
                    ->image()
                    ->directory('expertise')
                    ->imageResizeMode('cover')
                    // ->resize(400, 600)
                    ->imageEditor()
                    ->imageEditorAspectRatios([
                        '16:9',
                        '4:3',
                        '1:1',
                    ]),

                Forms\Components\TextInput::make('image_alt')
                    ->maxLength(200),

                 Forms\Components\Fieldset::make('SEO')
                ->columnSpan('full')
                ->columns(['default' => 1])
                ->schema([
                     Forms\Components\TextInput::make('page_title')
                        ->label('Page Title')
                        ->columnSpanFull(),
                    Forms\Components\Textarea::make('page_description')
                    ->label('Meta Description')
                        ->columnSpanFull(),
                    Forms\Components\Textarea::make('page_keywords')
                    ->label('Meta Keywords')
                        ->columnSpanFull(),
                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
               ImageColumn::make('image_url')
                ->label('Image')
                ->size(50)
                ->circular()
                ->visibility('public'),
                TextColumn::make('title')
                ->words(5)
                ->searchable(),
                TextColumn::make('content')
                ->html()
                ->words(15)
                ->wrap(),

                IconSelectColumn::make('status')
                ->closeOnSelection()
                ->options(PostStatus::class)
            ])
            ->recordUrl(null)
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->after(function(Expertise $expertise) {
                    if ($expertise->image_url) {
                        Storage::disk('public')->delete($expertise->image_url);
                     }
                }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListExpertises::route('/'),
            'create' => Pages\CreateExpertise::route('/create'),
            'edit' => Pages\EditExpertise::route('/{record}/edit'),
        ];
    }
}