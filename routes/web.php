<?php

use App\Http\Controllers\CareerController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Artisan;
use App\Http\Controllers\ExpertiseController;
use App\Http\Controllers\AboutController;
use App\Http\Controllers\PeopleController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\WelcomeController;


Route::get('/', [WelcomeController::class, 'index']);

Route::prefix('about')->name('about.')->group(function () {
    Route::get('who-we-are', [AboutController::class, 'index'])->name('who-we-are');
    Route::get('why-choose-us', [AboutController::class, 'chooseUs'])->name('why-choose-us');

    Route::get(
        'our-approach',
        function () {
            return view('about.approach');
        }
    )->name('our-approach');
     Route::get(
        'safety-measures',
        function () {
            return view('about.safety');
        }
    )->name('safety-measures');
    Route::get('our-team', [TeamController::class, 'index'])->name('our-team');
    Route::get('careers', [CareerController::class, 'index'])->name('careers');
});

Route::prefix('expertise')->name('expertise.')->group(function () {
    Route::get('asa-areas-of-expertise', [ExpertiseController::class, 'index'])->name('asa-areas-of-expertise');
    Route::get('expertise/{slug}', [ExpertiseController::class, 'show'])->name('expertise');
});

Route::prefix('people')->name('people.')->group(function () {
    Route::get('asa-attorneys', [PeopleController::class, 'index'])->name('asa-attorneys');
    // Route::get('people/{slug}', [PeopleController::class, 'show'])->name('people');
});

Route::prefix('services')->name('services.')->group(function () {
    Route::get('our-services', [ServiceController::class, 'index'])->name('our-services');
    Route::get('service/{slug}', [ServiceController::class, 'show'])->name('service');
});

Route::prefix('portfolio')->name('portfolio.')->group(function () {
    Route::get('our-portfolio', [PortfolioController::class, 'index'])->name('our-portfolio');
    Route::get('portfolio/{slug}', [PortfolioController::class, 'show'])->name('portfolio');
});

Route::prefix('contact')->name('contact.')->group(function () {
    Route::get('contact-us', [ContactController::class, 'index'])->name('contact-us');
    Route::post('send-enquiry', [ContactController::class, 'sendContact'])->name('enquiry');
});


// Route::get('/optimize', function () {
//     $status = Artisan::call('opitmize:clear');
//     return '<h1>Configurations cleared</h1>';
// });

/**
 * Route to create the link from the storage folder to the public folder
 * on cpanel
 */
// Route::get('/optimize', function () {
//     $storageFolder = storage_path('app/public');
//     $linkFolder = $_SERVER['DOCUMENT_ROOT']. '/storage';
//     symlink($storageFolder,$linkFolder);
//     return '<h1>Storage link created</h1>';
// });