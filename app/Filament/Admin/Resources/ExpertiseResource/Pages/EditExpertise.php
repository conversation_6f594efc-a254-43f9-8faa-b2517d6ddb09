<?php

namespace App\Filament\Admin\Resources\ExpertiseResource\Pages;

use App\Filament\Admin\Resources\ExpertiseResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditExpertise extends EditRecord
{
    protected static string $resource = ExpertiseResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\DeleteAction::make(),
        ];
    }

     protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
