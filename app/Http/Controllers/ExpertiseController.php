<?php

namespace App\Http\Controllers;

use App\Models\Expertise;
use Illuminate\Http\Request;

class ExpertiseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $expertises = Expertise::select('title', 'slug', 'content', 'icon', 'image_url')->where('status', 'published')->get();
        $titles = Expertise::select('title', 'slug')->where('status', 'published')->get();
        return view('expertise.index')->with([
            'expertises' => $expertises,
            'titles' => $titles
        ]);
    }



    /**
     * Display the specified resource.
     */
    public function show($slug)
    {
        $expertise = Expertise::where('slug',$slug)->where( 'status', 'published')->first();
        $allExpertises = Expertise::select('title', 'slug')->where( 'status', 'published')->orderByDesc('title')->get();
        return view('expertise.detail')->with([
            'expertise' => $expertise,
            'allExpertises' => $allExpertises
        ]);
    }
}
