/*--------------------------------------------------------------
>>> TABLE OF CONTENTS:
----------------------------------------------------------------
# Utility
# Cards
# Common
# Form
# Navigations
# Animations
# Mobile Nav
# Search Popup
# Page Header
# Google Map
# Client Carousel
--------------------------------------------------------------*/
:root {
  --wallox-font: "Plus Jakarta Sans", sans-serif;
  --wallox-heading-font: "Plus Jakarta Sans", sans-serif;
  --wallox-special-font: "Outfit", sans-serif;
  --wallox-text: #7E7C76;
  --wallox-text-rgb: 126, 124, 118;
  --wallox-text-dark: #2E2A20;
  --wallox-text-dark-rgb: 46, 42, 32;
  --wallox-text-gray: #89868d;
  --wallox-text-gray-rgb: 137, 134, 141;
  --wallox-text-gray2: #D9D9D9;
  --wallox-text-gray2-rgb: 217, 217, 217;
  --wallox-base: #DF9E42;
  --wallox-base-rgb: 223, 158, 66;
  --wallox-gray: #F4EDE4;
  --wallox-gray-rgb: 244, 237, 228;
  --wallox-white: #fff;
  --wallox-white-rgb: 255, 255, 255;
  --wallox-border-color: #E4DACC;
  --wallox-border-color-rgb: 228, 218, 204;
  --wallox-letter-space: 0.1em;
  --wallox-letter-space-xl: 0.2em;
}

/*--------------------------------------------------------------
# Utility
--------------------------------------------------------------*/
.mt-20 {
  margin-top: 20px;
}

.mt-30 {
  margin-top: 30px;
}

.mt-40 {
  margin-top: 40px;
}

.mt-50 {
  margin-top: 50px;
}

.mt-60 {
  margin-top: 60px;
}

.mt-80 {
  margin-top: 80px;
}

.mt-120 {
  margin-top: 120px;
}

.mt--60 {
  margin-top: -60px;
}

.mt--120 {
  margin-top: -120px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mb-50 {
  margin-bottom: 50px;
}

.mb-60 {
  margin-bottom: 60px;
}

.mb-80 {
  margin-bottom: 80px;
}

.mb-120 {
  margin-bottom: 120px;
}

.mb--60 {
  margin-bottom: -60px;
}

.mb--120 {
  margin-bottom: -120px;
}

.pt-20 {
  padding-top: 20px;
}

.pt-30 {
  padding-top: 30px;
}

.pt-40 {
  padding-top: 40px;
}

.pt-50 {
  padding-top: 50px;
}

.pt-60 {
  padding-top: 60px;
}

.pt-80 {
  padding-top: 80px;
}

.pt-100 {
  padding-top: 100px;
}

.pt-110 {
  padding-top: 110px;
}

.pt-115 {
  padding-top: 115px;
}

.pt-120 {
  padding-top: 120px;
}

.pt-142 {
  padding-top: 142px;
}

.pb-20 {
  padding-bottom: 20px;
}

.pb-30 {
  padding-bottom: 30px;
}

.pb-40 {
  padding-bottom: 40px;
}

.pb-50 {
  padding-bottom: 50px;
}

.pb-60 {
  padding-bottom: 60px;
}

.pb-80 {
  padding-bottom: 80px;
}

.pb-90 {
  padding-bottom: 90px;
}

.pb-100 {
  padding-bottom: 100px;
}

.pb-110 {
  padding-bottom: 110px;
}

.pb-115 {
  padding-bottom: 115px;
}

.pb-120 {
  padding-bottom: 120px;
}

.pl-5 {
  padding-left: 5px;
}

.pl-10 {
  padding-left: 10px;
}

.pl-15 {
  padding-left: 15px;
}

.pl-20 {
  padding-left: 20px;
}

.pl-30 {
  padding-left: 30px;
}

.pr-5 {
  padding-right: 5px;
}

.pr-10 {
  padding-right: 10px;
}

.pr-15 {
  padding-right: 15px;
}

.pr-20 {
  padding-right: 20px;
}

.pr-30 {
  padding-right: 30px;
}

.wallox-btn {
  display: inline-block;
  vertical-align: middle;
  -webkit-appearance: none;
  border: none;
  outline: none !important;
  background-color: var(--wallox-text-dark, #2E2A20);
  color: #fff;
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  padding: 15px 24px;
  transition: 500ms;
  background-color: var(--wallox-text-dark, #2E2A20);
  color: #fff;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 1.25;
  text-transform: capitalize;
  border-radius: 100px;
  position: relative;
  overflow: hidden;
  text-align: center;
}
.wallox-btn:hover {
  color: var(--wallox-text-dark, #2E2A20);
  background-color: #fff;
}
.wallox-btn::before {
  content: "";
  background-color: var(--wallox-base, #DF9E42);
  border-radius: 100px;
  display: block;
  height: 100%;
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
  transform: translate(-100%, 0) rotate(10deg);
  transform-origin: top left;
  transition: 0.2s transform ease-out;
  will-change: transform;
  z-index: -1;
}
.wallox-btn--base {
  background: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
}
.wallox-btn--base::before {
  background-color: var(--wallox-text-dark, #2E2A20);
}
.wallox-btn--base:hover {
  color: var(--wallox-white, #fff);
}
.wallox-btn--border {
  background: transparent;
  border: 2px solid var(--wallox-text, #7E7C76);
}
.wallox-btn--border:hover {
  color: var(--wallox-white, #fff);
  border-color: transparent;
}
.wallox-btn:hover {
  color: var(--wallox-white, #fff);
  background: transparent;
  transform: scale(1.05);
  will-change: transform;
}
.wallox-btn:hover::before {
  transform: translate(0, 0);
}

/*--------------------------------------------------------------
# Common
--------------------------------------------------------------*/
body {
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  color: var(--wallox-text, #7E7C76);
  font-size: 16px;
  line-height: 2.125;
  font-weight: 500;
}

body.locked {
  overflow: hidden;
}

a {
  color: var(--wallox-base, #DF9E42);
  transition: all 400ms ease;
}

a,
a:hover,
a:focus,
a:visited {
  text-decoration: none;
}

::placeholder {
  color: inherit;
  opacity: 1;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--wallox-heading-font, "Plus Jakarta Sans", serif);
  color: var(--wallox-text-dark, #2E2A20);
}
@media (max-width: 575px) {
  h1 br,
  h2 br,
  h3 br,
  h4 br,
  h5 br,
  h6 br {
    display: none;
  }
}

@media (max-width: 575px) {
  p br {
    display: none;
  }
}

::placeholder {
  color: inherit;
  opacity: 1;
}

.page-wrapper {
  position: relative;
  margin: 0 auto;
  width: 100%;
  min-width: 300px;
  overflow: hidden;
}

.container-fluid,
.container {
  padding-left: 15px;
  padding-right: 15px;
}

@media (min-width: 1200px) {
  .container {
    max-width: 1200px;
  }
}
.row {
  --bs-gutter-x: 30px;
}

.gutter-y-10 {
  --bs-gutter-y: 10px;
}

.gutter-y-15 {
  --bs-gutter-y: 15px;
}

.gutter-y-20 {
  --bs-gutter-y: 20px;
}

.gutter-y-30 {
  --bs-gutter-y: 30px;
}

.gutter-y-60 {
  --bs-gutter-y: 60px;
}

.text-start {
  text-align: left !important;
}

.tabs-box .tabs-content .tab:not(.active-tab) {
  display: none;
}

.bootstrap-select .dropdown-menu {
  padding-top: 0;
  padding-bottom: 0;
  border-radius: 0;
}
.bootstrap-select .dropdown-item.active,
.bootstrap-select .dropdown-item:active {
  background-color: var(--wallox-base, #DF9E42);
}

/* tiny slider button customize */
.tns-outer .tns-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 40px;
}
.tns-outer .tns-controls button {
  width: 45px;
  height: 45px;
  border: 2px solid #f4f4f4;
  outline: none;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--wallox-text, #7E7C76);
  border-radius: 50%;
  margin-left: 5px;
  margin-right: 5px;
}

.block-title {
  margin-top: -8px;
  margin-bottom: 50px;
}
.block-title__decor {
  width: 21px;
  height: 14px;
  background-image: url(../images/shapes/leaf-1-1.png);
  background-repeat: no-repeat;
  background-position: top center;
  display: inline-block;
  line-height: 1;
  margin-bottom: -5px;
  position: relative;
  top: -7px;
}
.block-title p {
  margin: 0;
  color: var(--wallox-text, #7E7C76);
  font-size: 16px;
  line-height: 1;
  margin-bottom: 7px;
}
@media (min-width: 768px) {
  .block-title p {
    font-size: 18px;
  }
}
@media (min-width: 992px) {
  .block-title p {
    font-size: 20px;
  }
}
.block-title h3 {
  margin: 0;
  font-size: 35px;
  color: var(--wallox-text-dark, #2E2A20);
  font-family: var(--wallox-special-font, "Outfit", sans-serif);
}
@media (min-width: 768px) {
  .block-title h3 {
    font-size: 42px;
  }
}
@media (min-width: 992px) {
  .block-title h3 {
    font-size: 50px;
  }
}

.ul-list-one {
  margin-bottom: 0;
}
.ul-list-one li {
  position: relative;
  padding-left: 45px;
  font-size: 16px;
  font-weight: 500;
  color: var(--wallox-text-dark, #2E2A20);
}
@media (min-width: 481px) {
  .ul-list-one li {
    font-size: 20px;
  }
}
.ul-list-one li::before {
  content: "\e907";
  color: var(--wallox-base, #DF9E42);
  font-size: 26px;
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  font-family: "azino-icon";
}

.preloader {
  position: fixed;
  background-color: var(--wallox-text-dark, #2E2A20);
  background-position: center center;
  background-repeat: no-repeat;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9991;
  display: -webkit-box;
  display: flex;
  -webkit-box-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  align-items: center;
  text-align: center;
}
.preloader__image {
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation-name: flipInY;
  animation-name: flipInY;
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 60px auto;
  width: 100%;
  height: 100%;
}

/* scroll to top */
.scroll-to-top {
  display: flex;
  align-items: center;
  width: auto;
  height: 35px;
  background: transparent;
  position: fixed;
  bottom: 60px;
  right: -12px;
  z-index: 99;
  text-align: center;
  opacity: 0;
  visibility: hidden;
  transform: rotate(-90deg);
  cursor: pointer;
  transition: all 0.2s ease;
}
.scroll-to-top__text {
  display: inline;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-weight: 700;
  margin-right: 8px;
}
.scroll-to-top__wrapper {
  display: inline-block;
  width: 30px;
  height: 4px;
  background-color: var(--wallox-base, #DF9E42);
  position: relative;
  overflow: hidden;
}
.scroll-to-top__inner {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: var(--wallox-text-dark, #2E2A20);
}
.scroll-to-top.show {
  opacity: 1;
  visibility: visible;
  bottom: 70px;
}

/* post paginations */
.post-pagination {
  margin: 0;
  padding: 0;
  list-style: none;
  margin-top: 50px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 11px;
}
.post-pagination li:last-child a:hover, .post-pagination li:first-child a:hover {
  background: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
}
.post-pagination a {
  width: 40px;
  height: 40px;
  background-color: var(--wallox-gray, #F4EDE4);
  color: var(--wallox-text, #7E7C76);
  text-transform: capitalize;
  text-align: center;
  border-radius: 50%;
  border: 1px solid transparent;
  transition: all 400ms ease;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.post-pagination a:hover {
  background: transparent;
  color: var(--wallox-base, #DF9E42);
  border: 1px solid var(--wallox-base, #DF9E42);
}
.post-pagination a i {
  line-height: 1;
  font-size: 14px;
}

/* Owl Shadow */
.wallox-owl__carousel--with-shadow .owl-stage-outer {
  overflow: visible;
}
.wallox-owl__carousel--with-shadow .owl-item {
  opacity: 0;
  visibility: hidden;
  transition: opacity 500ms ease, visibility 500ms ease;
}
.wallox-owl__carousel--with-shadow .owl-item.active {
  opacity: 1;
  visibility: visible;
}

/* Owl Nav */
.wallox-owl__carousel--basic-nav.owl-carousel .owl-nav {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 60px;
}
.wallox-owl__carousel--basic-nav.owl-carousel .owl-nav button {
  border: none;
  outline: none;
  border-radius: 50%;
  margin: 0;
  padding: 0;
}
.wallox-owl__carousel--basic-nav.owl-carousel .owl-nav button span {
  border: none;
  outline: none;
  width: 50px;
  height: 50px;
  background-color: var(--wallox-gray, #F4EDE4);
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--wallox-text, #7E7C76);
  border-radius: 50%;
  font-size: 14px;
  color: var(--wallox-text, #7E7C76);
  transition: all 500ms ease;
}
.wallox-owl__carousel--basic-nav.owl-carousel .owl-nav button span:hover {
  background-color: var(--wallox-text-dark, #2E2A20);
  color: var(--wallox-white, #fff);
}
.wallox-owl__carousel--basic-nav.owl-carousel .owl-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-top: 60px;
}
.wallox-owl__carousel--basic-nav.owl-carousel .owl-dots .owl-dot span {
  background-color: var(--wallox-gray2, #D9D9D9);
  width: 10px;
  border-radius: 50%;
  height: 10px;
  margin: 0;
}
.wallox-owl__carousel--basic-nav.owl-carousel .owl-dots .owl-dot:hover span, .wallox-owl__carousel--basic-nav.owl-carousel .owl-dots .owl-dot.active span {
  background-color: var(--wallox-base, #DF9E42);
}
.wallox-owl__carousel--basic-nav.owl-carousel .owl-nav.disabled + .owl-dots {
  margin-top: 60px;
}

.wallox-owl__carousel__counter {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: -80%;
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
  gap: 19px;
}
@media (max-width: 1199px) {
  .wallox-owl__carousel__counter {
    left: -40%;
  }
}
@media (max-width: 991px) {
  .wallox-owl__carousel__counter {
    left: -20%;
  }
}
@media (max-width: 767px) {
  .wallox-owl__carousel__counter {
    display: none;
  }
}
.wallox-owl__carousel__counter .wallox-owl__carousel__counter__total {
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  font-style: normal;
  font-weight: 600;
  font-size: 24px;
  line-height: 100%;
  color: var(--wallox-text-dark, #2E2A20);
  text-transform: uppercase;
  position: relative;
}
.wallox-owl__carousel__counter .wallox-owl__carousel__counter__total::before {
  content: "";
  position: absolute;
  top: calc(50% + 1px);
  left: -10px;
  transform: translateY(-50%) rotate(20deg);
  width: 1px;
  height: 16px;
  background-color: var(--wallox-border-color, #E4DACC);
}
.wallox-owl__carousel__counter .wallox-owl__carousel__counter__current {
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  font-style: normal;
  font-weight: 800;
  font-size: 24px;
  line-height: 67%;
  position: relative;
  color: var(--wallox-base, #DF9E42);
  text-transform: uppercase;
}

/* Section Title */
.sec-title {
  padding-bottom: 55px;
}
@media (max-width: 767px) {
  .sec-title {
    padding-bottom: 35px;
  }
}
@media (max-width: 575px) {
  .sec-title {
    padding-bottom: 30px;
  }
}
.sec-title__image {
  margin-right: 10px;
}
.sec-title__tagline {
  margin: 0;
  color: var(--wallox-base, #DF9E42);
  letter-spacing: 0.15em;
  text-transform: uppercase;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 1.25;
}
.sec-title__title {
  margin-top: 11px;
  color: var(--wallox-text-dark, #2E2A20);
  font-style: normal;
  font-weight: 800;
  font-size: 45px;
  line-height: 1.2666666667;
  letter-spacing: -0.02em;
  margin-bottom: 0;
  padding-bottom: 0;
}
@media (max-width: 767px) {
  .sec-title__title {
    font-size: 40px;
  }
}
@media (max-width: 575px) {
  .sec-title__title {
    font-size: 35px;
  }
}

.wallox-slick__carousel--with-shadow .slick-list {
  overflow: visible;
}
.wallox-slick__carousel--with-shadow .slick-slide {
  opacity: 0;
  visibility: hidden;
  transition: opacity 500ms ease, visibility 500ms ease;
}
.wallox-slick__carousel--with-shadow .slick-slide.slick-active {
  opacity: 1;
  visibility: visible;
}

.ui-datepicker .ui-datepicker-header {
  background-image: none;
  background-color: var(--wallox-text-dark, #2E2A20);
  color: var(--wallox-white, #fff);
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
}

.ui-datepicker-calendar th span {
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
}
.ui-datepicker-calendar td {
  background-color: var(--wallox-gray, #F4EDE4);
  background-image: none;
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  color: var(--wallox-text, #7E7C76);
}
.ui-datepicker-calendar td a {
  border-color: var(--wallox-border-color, #E4DACC);
  background-color: var(--wallox-gray, #F4EDE4);
  background-image: none;
}
.ui-datepicker-calendar .ui-state-default,
.ui-datepicker-calendar .ui-widget-content .ui-state-default,
.ui-datepicker-calendar .ui-widget-header .ui-state-default {
  border-color: var(--wallox-border-color, #E4DACC);
  background-color: var(--wallox-gray, #F4EDE4);
  background-image: none;
  color: var(--wallox-text, #7E7C76);
  padding: 10px 5px;
  text-align: center;
  line-height: 1em;
}
.ui-datepicker-calendar .ui-state-default:hover,
.ui-datepicker-calendar .ui-widget-content .ui-state-default:hover,
.ui-datepicker-calendar .ui-widget-header .ui-state-default:hover {
  color: var(--wallox-white, #fff);
  background-color: var(--wallox-base, #DF9E42);
}
.ui-datepicker-calendar .ui-state-highlight,
.ui-datepicker-calendar .ui-widget-content .ui-state-highlight,
.ui-datepicker-calendar .ui-widget-header .ui-state-highlight {
  color: var(--wallox-white, #fff);
  background-color: var(--wallox-base, #DF9E42);
}

.ui-datepicker .ui-datepicker-prev,
.ui-datepicker .ui-datepicker-next {
  background-image: none;
  background-color: var(--wallox-white, #fff);
  color: var(--wallox-text-dark, #2E2A20);
}
.ui-datepicker .ui-datepicker-prev:hover,
.ui-datepicker .ui-datepicker-next:hover {
  background-color: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
  top: 2px;
}

.ui-datepicker .ui-datepicker-prev:hover {
  left: 2px;
}

.ui-datepicker .ui-datepicker-next:hover {
  right: 2px;
}

/*--------------------------------------------------------------
# Cards
--------------------------------------------------------------*/
.team-card {
  position: relative;
  overflow: hidden;
  border-radius: 20px;
  z-index: 4;
}
.team-card__social {
  position: absolute;
  top: 30px;
  left: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  flex-direction: column;
  transform: translateX(-200%);
  transition: all 0.4s ease-in-out;
}
.team-card__social a {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--wallox-white, #fff);
  box-shadow: 0px 4px 25px rgba(0, 0, 0, 0.06);
  font-size: 18px;
  color: var(--wallox-base, #DF9E42);
  position: relative;
  transition: all 0.4s ease-in-out;
  z-index: 1;
}
.team-card__social a::after {
  content: "";
  width: 0;
  height: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  background: var(--wallox-base, #DF9E42);
  border-radius: 50%;
  transition: all 0.4s ease-in-out;
  z-index: -1;
}
.team-card__social a:hover {
  color: var(--wallox-white, #fff);
}
.team-card__social a:hover::after {
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
.team-card:hover .team-card__thumb img {
  transform: scale(1.1);
}
.team-card:hover .team-card__thumb::after {
  transform: translateY(0%);
}
.team-card:hover .team-card__social {
  transform: translateX(0%);
}
.team-card:hover .team-card__content {
  background: var(--wallox-base, #DF9E42);
}
.team-card:hover .team-card__content .team-card__content__title,
.team-card:hover .team-card__content .team-card__content__dec {
  color: var(--wallox-white, #fff);
}
.team-card__thumb {
  position: relative;
  border-radius: 20px;
}
.team-card__thumb img {
  position: relative;
  object-fit: cover;
  width: 100%;
  transform: scale(1);
  transition: all 0.4s ease-in-out;
}
.team-card__thumb::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  transform: translateY(-100%);
  left: 0;
  top: 0;
  background-color: rgba(var(--wallox-text-dark-rgb, 46, 42, 32), 0.7);
  transition: all 0.4s ease-in-out;
}
.team-card__content {
  border-radius: 10px;
  background: var(--wallox-white, #fff);
  padding: 23px 15px;
  position: absolute;
  transform: rotate(-180deg);
  position: absolute;
  writing-mode: vertical-rl;
  bottom: 30px;
  right: 30px;
  transition: all 0.4s ease-in-out;
  min-height: 209px;
}
.team-card__content__title {
  margin-left: 0;
  margin-right: 0;
  margin-top: -7px;
  font-style: normal;
  font-weight: 600;
  font-size: 22px;
  line-height: 136%;
  margin-bottom: 0px;
  padding-bottom: 0;
  color: var(--wallox-text-dark, #2E2A20);
  text-transform: capitalize;
}
.team-card__content__title a {
  color: inherit;
}
.team-card__content__title a:hover {
  color: var(--wallox-text-dark, #2E2A20);
}
.team-card__content__dec {
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 162%;
  color: var(--wallox-text, #7E7C76);
  margin-bottom: -5px;
  text-transform: capitalize;
  transition: all 0.4s ease-in-out;
}

.team-one {
  padding-top: 120px;
  padding-bottom: 120px;
  position: relative;
}
@media (max-width: 991px) {
  .team-one {
    padding: 100px 0px;
  }
}
@media (max-width: 767px) {
  .team-one {
    padding: 80px 0px;
  }
}
.team-one--home {
  background-color: var(--wallox-text-dark, #2E2A20);
}
.team-one--three {
  background-color: var(--wallox-text-dark, #2E2A20);
  padding-top: 270px;
  padding-bottom: 0;
}
@media (max-width: 1199px) {
  .team-one--three {
    padding-top: 250px;
    padding-bottom: 0;
  }
}
@media (max-width: 991px) {
  .team-one--three {
    padding-top: 230px;
    padding-bottom: 0;
  }
}
@media (max-width: 767px) {
  .team-one--three {
    padding-top: 220px;
    padding-bottom: 0;
  }
}
.team-one__bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-repeat: no-repeat;
  background-size: cover;
  mix-blend-mode: overlay;
  opacity: 0.4;
}
.team-one .sec-title {
  padding-bottom: 0;
}
.team-one .sec-title .sec-title__title {
  font-style: normal;
  font-weight: 800;
  font-size: 45px;
  line-height: 57px;
  color: var(--wallox-white, #fff);
}
.team-one__top {
  margin-bottom: 50px;
}
.team-one__top__btn {
  text-align: end;
}
@media (max-width: 991px) {
  .team-one__top__btn {
    text-align: start;
  }
}
@media (min-width: 992px) {
  .team-one__carousel .owl-nav {
    display: none;
  }
}

/*team-details*/
.team-details {
  padding-top: 120px;
  padding-bottom: 120px;
  background-color: var(--wallox-white, #fff);
}
@media (max-width: 991px) {
  .team-details {
    padding-top: 100px;
    padding-bottom: 100px;
  }
}
@media (max-width: 767px) {
  .team-details {
    padding-top: 80px;
    padding-bottom: 80px;
  }
}
.team-details__inner {
  background: var(--wallox-gray, #F4EDE4);
  position: relative;
  z-index: 1;
  padding: 30px;
  border-radius: 20px;
  z-index: 1;
}
@media (max-width: 991px) {
  .team-details__inner {
    padding: 30px 20px;
  }
}
.team-details__inner::after {
  position: absolute;
  top: 0;
  left: 0;
  content: "";
  width: 100%;
  height: 100%;
  background-image: url(../images/shapes/team-details-shape.png);
  background-position: center center;
  background-repeat: no-repeat;
  mix-blend-mode: multiply;
  z-index: -1;
  opacity: 0.1;
}
.team-details__thumb {
  position: relative;
  overflow: hidden;
  margin-right: -15px;
  border-radius: 20px;
}
@media (max-width: 991px) {
  .team-details__thumb {
    margin-right: 0;
  }
}
.team-details__thumb__image {
  position: relative;
}
.team-details__thumb__image img {
  object-fit: cover;
  width: 100%;
}
.team-details__thumb__image:hover .team-details__thumb__social {
  transform: translate(-50%, 0%);
}
.team-details__thumb__social {
  position: absolute;
  bottom: 40px;
  left: 50%;
  gap: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  transform: translate(-50%, 200%);
  transition: all 0.4s ease-in-out;
  text-align: center;
}
.team-details__thumb__social a {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--wallox-white, #fff);
  box-shadow: 0px 4px 25px rgba(0, 0, 0, 0.06);
  font-size: 18px;
  color: var(--wallox-base, #DF9E42);
  position: relative;
  transition: all 0.4s ease-in-out;
  z-index: 1;
}
.team-details__thumb__social a::after {
  content: "";
  width: 0;
  height: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  background: var(--wallox-base, #DF9E42);
  border-radius: 50%;
  transition: all 0.4s ease-in-out;
  z-index: -1;
}
.team-details__thumb__social a:hover {
  color: var(--wallox-white, #fff);
}
.team-details__thumb__social a:hover::after {
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
.team-details__content {
  margin-left: 15px;
}
@media (max-width: 991px) {
  .team-details__content {
    margin-left: 0px;
  }
}
.team-details__content__about {
  border-bottom: 1px solid var(--wallox-border-color, #E4DACC);
  padding-bottom: 22px;
  margin-bottom: 30px;
}
.team-details__content__title {
  border-bottom: 1px solid var(--wallox-border-color, #E4DACC);
  padding-bottom: 28px;
  margin-bottom: 20px;
  color: var(--wallox-text-dark, #2E2A20);
  text-transform: capitalize;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 100%;
}
.team-details__content__text {
  font-style: normal;
  margin-bottom: 0;
  padding-bottom: 0;
  color: var(--wallox-text, #7E7C76);
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 175%;
}
.team-details__content__text + .team-details__content__text {
  margin-top: 10px;
}
.team-details__content__signeture {
  margin-top: 35px;
  margin-bottom: 5px;
}
.team-details__link {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0px;
}
@media (max-width: 767px) {
  .team-details__link {
    flex-direction: column;
    justify-content: start;
    align-items: start;
    gap: 10px;
  }
}
.team-details__link__item__inner {
  display: flex;
  align-items: center;
  gap: 10px;
}
.team-details__link__item__inner:hover .team-details__link__icon {
  color: var(--wallox-white, #fff);
}
.team-details__link__item__inner:hover .team-details__link__icon::after {
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
.team-details__link__icon {
  width: 45px;
  max-width: 45px;
  height: 45px;
  border-radius: 50%;
  background: var(--wallox-white, #fff);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  color: var(--wallox-base, #DF9E42);
  position: relative;
  transition: all 0.4s ease-in-out;
  z-index: 1;
}
.team-details__link__icon::after {
  content: "";
  width: 0%;
  height: 0%;
  background: var(--wallox-base, #DF9E42);
  position: absolute;
  top: 50%;
  left: 50%;
  border-radius: 50%;
  transition: all 0.4s ease-in-out;
  z-index: -1;
}
.team-details__link a,
.team-details__link p {
  font-style: normal;
  color: var(--wallox-text-dark, #2E2A20);
  margin-bottom: 0;
  padding-bottom: 0;
  font-weight: 600;
  font-size: 16px;
  line-height: 100%;
}
.team-details__link a {
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.team-details__link a:hover {
  background-size: 100% 1px;
}
.team-details__link a:hover {
  color: var(--wallox-base, #DF9E42);
}

/*team-details*/
.team-skills-one {
  position: relative;
  padding: 60px 0px;
  background: var(--wallox-gray, #F4EDE4);
}
.team-skills-one__top {
  margin-bottom: 50px;
}
.team-skills-one__top__title {
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 120%;
  letter-spacing: -0.02em;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
  text-shadow: 0 0 0.1px currentColor;
  margin-bottom: 18px;
}
.team-skills-one__top__text {
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 188%;
  margin-bottom: 0;
  padding-bottom: 0;
  color: var(--wallox-text, #7E7C76);
}
.team-skills-one__top__text + .team-skills-one__top__text {
  margin-top: 29px;
}
@media (max-width: 991px) {
  .team-skills-one__top__text + .team-skills-one__top__text {
    margin-top: 20px;
  }
}
.team-skills-one__inner {
  background: var(--wallox-white, #fff);
  padding: 30px 40px;
}
@media (max-width: 991px) {
  .team-skills-one__inner {
    padding: 20px;
  }
}
.team-skills-one__inner__title {
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 120%;
  letter-spacing: -0.02em;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
  text-shadow: 0 0 0.1px currentColor;
  margin-bottom: 27px;
}
.team-skills-one__skills {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 20px 31px;
  margin-bottom: 42px;
}
@media (max-width: 991px) {
  .team-skills-one__skills {
    grid-template-columns: 1fr;
    grid-gap: 20px;
  }
}

/*team-Progress*/
.progress-box__title {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 188%;
  color: var(--wallox-text-dark, #2E2A20);
  text-transform: capitalize;
  margin-bottom: 0;
  padding-bottom: 0;
}
.progress-box__bar {
  width: 100%;
  height: 10px;
  background-color: var(--wallox-gray, #F4EDE4);
  position: relative;
}
.progress-box__bar__inner {
  position: absolute;
  height: 100%;
  top: 0;
  left: 0;
  background-color: var(--wallox-base, #DF9E42);
  transition: all 800ms linear;
  width: 0px;
}
.progress-box__number {
  position: absolute;
  bottom: calc(100% + 2px);
  right: 0;
  font-size: 16px;
  color: var(--wallox-text-dark, #2E2A20);
  font-weight: 700;
  line-height: 1.875;
  text-transform: uppercase;
}

/*Team Two*/
.team-two {
  padding-top: 120px;
  padding-bottom: 120px;
  position: relative;
  z-index: 1;
}
@media (max-width: 991px) {
  .team-two {
    padding-top: 100px;
    padding-bottom: 100px;
  }
}
@media (max-width: 767px) {
  .team-two {
    padding-top: 80px;
    padding-bottom: 80px;
  }
}
.team-two__top {
  border-bottom: 1px solid var(--wallox-border-color, #E4DACC);
  padding-bottom: 30px;
  margin-bottom: 60px;
}
@media (max-width: 991px) {
  .team-two__top {
    margin-bottom: 40px;
  }
}
@media (max-width: 767px) {
  .team-two__top {
    margin-bottom: 30px;
  }
}
.team-two__top .sec-title {
  padding-bottom: 0;
}
@media (max-width: 991px) {
  .team-two__top .sec-title {
    padding-bottom: 30px;
  }
}
.team-two__top__right {
  display: flex;
  align-items: center;
  justify-content: end;
}
@media (max-width: 991px) {
  .team-two__top__right {
    justify-content: start;
  }
}
.team-two__top__right .wallox-btn {
  color: var(--wallox-base, #DF9E42);
  border: 1px solid var(--wallox-base, #DF9E42);
}
.team-two__top__right .wallox-btn:hover {
  color: var(--wallox-white, #fff);
}
.team-two__item {
  display: block;
  cursor: pointer;
}
.team-two__item__subtitle {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  display: block;
  color: var(--wallox-base, #DF9E42);
  letter-spacing: 0.15em;
  text-transform: uppercase;
}
.team-two__item__title {
  font-style: normal;
  font-weight: 800;
  font-size: 45px;
  line-height: 57px;
  margin-bottom: 0;
  padding-bottom: 0;
  color: transparent;
  -webkit-text-stroke: 1px var(--wallox-text, #7E7C76);
  letter-spacing: -0.02em;
  text-transform: uppercase;
  transition: all 0.4s ease-in-out;
}
.team-two__item__title:hover {
  color: var(--wallox-base, #DF9E42);
  -webkit-text-stroke: 1px var(--wallox-base, #DF9E42);
}
.team-two__item.active .team-two__item__title {
  color: var(--wallox-base, #DF9E42);
  -webkit-text-stroke: 1px var(--wallox-base, #DF9E42);
}
.team-two__item + .team-two__item {
  margin-top: 30px;
  border-top: 1px solid var(--wallox-border-color, #E4DACC);
  padding-top: 25px;
}
.team-two__author {
  display: grid;
  grid-template-columns: auto auto;
  gap: 30px;
}
@media (max-width: 575px) {
  .team-two__author {
    grid-template-columns: auto;
    max-width: 400px;
    width: 100%;
    margin: 0 auto;
  }
}
.team-two__author__item {
  position: relative;
}
.team-two__author__item::after {
  content: "";
  width: 100%;
  height: 100%;
  background-color: var(--wallox-base, #DF9E42);
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 20px;
  transform: translate(0, 0);
  z-index: -1;
  transition: all 0.4s ease-in-out;
}
.team-two__author__item__thumb {
  border-radius: 20px;
  overflow: hidden;
}
.team-two__author__item__thumb img {
  width: 100%;
  object-fit: cover;
}
.team-two__author__item__social {
  display: flex;
  align-self: center;
  justify-content: center;
  gap: 10px;
  position: absolute;
  bottom: 30px;
  transform: translate(20%, 200%);
  opacity: 0;
  transition: all 0.4s ease-in-out;
}
@media (max-width: 575px) {
  .team-two__author__item__social {
    left: 50%;
    transform: translate(-50%, 200%);
  }
}
.team-two__author__item__social a {
  width: 30px;
  border-radius: 50%;
  height: 30px;
  background: transparent;
  border: 1px solid var(--wallox-border-color, #E4DACC);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: var(--wallox-white, #fff);
  transition: all 0.4s ease-in-out;
}
.team-two__author__item__social a:hover {
  background-color: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
  border-color: var(--wallox-base, #DF9E42);
}
.team-two__author__item.expand {
  filter: blur(6px);
  -webkit-filter: blur(6px);
}
.team-two__author__item.active {
  filter: blur(0px);
  -webkit-filter: blur(0px);
}
.team-two__author__item.active::after {
  transform: translate(-6%, -6%);
}
.team-two__author__item.active .team-two__author__item__social {
  transform: translate(20%, 0%);
  opacity: 1;
}
.team-two__shape {
  position: absolute;
  top: 50%;
  left: 0;
  animation: topToBottom 3s ease-in-out infinite;
}
@media (max-width: 991px) {
  .team-two__shape {
    display: none;
  }
}
.team-two__shape-two {
  position: absolute;
  right: 0;
  bottom: 20%;
  animation: rotated 10s infinite linear;
  z-index: -1;
}
@media (max-width: 991px) {
  .team-two__shape-two {
    display: none;
  }
}
.team-two .author-item {
  opacity: 0.3;
  transition: all 0.4s ease-in-out;
}
.team-two .author-item.active {
  opacity: 1;
}

/*Blog Card One Style*/
.blog-card {
  position: relative;
  background-color: var(--wallox-gray, #F4EDE4);
  padding: 30px;
  border-radius: 20px;
  transition: all 0.4s ease-in-out;
}
.blog-card--two {
  background-color: var(--wallox-white, #fff);
}
.blog-card--two:hover {
  background-color: var(--wallox-gray, #F4EDE4);
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
}
@media (max-width: 1199px) and (min-width: 991px) {
  .blog-card {
    padding: 15px;
  }
}
@media (max-width: 500px) {
  .blog-card {
    padding: 15px;
  }
}
.blog-card__image {
  display: block;
  position: relative;
  overflow: hidden;
  border-radius: 20px;
  margin-bottom: 22px;
}
.blog-card__image img {
  width: 100%;
  object-fit: cover;
  transform: scale(1);
  transition: all 0.5s ease-in-out;
}
.blog-card__hover__box {
  background-color: rgba(var(--wallox-base-rgb, 223, 158, 66), 0.7);
  height: 100%;
  top: 0;
  opacity: 0;
  position: absolute;
  transform: scaleX(0);
  transition: all 0.5s ease 0s;
  width: 25%;
  z-index: 1;
}
.blog-card__hover__box--1 {
  left: 0;
  transition-delay: 0.105s;
}
.blog-card__hover__box--2 {
  left: 25%;
  transition-delay: 0.105s;
}
.blog-card__hover__box--3 {
  left: 50%;
  transition-delay: 0.105s;
}
.blog-card__hover__box--4 {
  left: 75%;
  transition-delay: 0s;
}
.blog-card__title {
  color: var(--wallox-text-dark, #2E2A20);
  padding-bottom: 0;
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 160%;
  letter-spacing: -0.02em;
  text-transform: capitalize;
  margin-bottom: 13px;
}
@media (max-width: 1199px) and (min-width: 992px) {
  .blog-card__title {
    font-size: 18px;
  }
}
@media (max-width: 400px) {
  .blog-card__title {
    font-size: 18px;
  }
}
.blog-card__title a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.blog-card__title a:hover {
  background-size: 100% 1px;
}
.blog-card__title a:hover {
  color: var(--wallox-base, #DF9E42);
}
.blog-card__meta {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 27px;
}
.blog-card__meta__item {
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  line-height: 133%;
  margin-bottom: 0;
  padding-bottom: 0;
  color: var(--wallox-text, #7E7C76);
}
.blog-card__meta__item a {
  color: inherit;
}
.blog-card__meta__item a:hover {
  color: var(--wallox-base, #DF9E42);
}
.blog-card__meta__item i {
  color: var(--wallox-base, #DF9E42);
  margin-right: 5px;
}
.blog-card__date {
  text-align: center;
  position: relative;
  background: var(--wallox-white, #fff);
  font-style: normal;
  font-weight: 700;
  font-size: 14px;
  line-height: 18px;
  margin-bottom: 0;
  padding-bottom: 0;
  color: var(--wallox-text-dark, #2E2A20);
  text-transform: uppercase;
  display: inline-block;
  border-radius: 10px;
  transition: all 0.4s ease-in-out;
}
.blog-card__date__day {
  padding: 3px 10px 5px 10px;
  color: var(--wallox-white, #fff);
  display: block;
  background: var(--wallox-base, #DF9E42);
  border-radius: 10px;
}
.blog-card__date__month {
  padding: 3px 10px 4px 10px;
  display: block;
}
.blog-card:hover {
  background-color: var(--wallox-white, #fff);
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
}
.blog-card:hover .blog-card__image img {
  transform: scale(1.1);
}
.blog-card:hover .blog-card__date {
  background: var(--wallox-gray, #F4EDE4);
}
.blog-card:hover .blog-card__hover__box {
  opacity: 1;
  transform: scale(1);
}

/*Blog Card two Style*/
.blog-card-two {
  border-bottom: 1px solid var(--wallox-border-color, #E4DACC);
  padding-bottom: 30px;
}
.blog-card-two__image {
  display: block;
  position: relative;
  overflow: hidden;
  border-radius: 20px;
  margin-bottom: 22px;
}
.blog-card-two__image img {
  width: 100%;
  object-fit: cover;
  transform: scale(1);
  transition: all 0.5s ease-in-out;
}
.blog-card-two__hover__box {
  background-color: rgba(var(--wallox-base-rgb, 223, 158, 66), 0.7);
  height: 100%;
  top: 0;
  opacity: 0;
  position: absolute;
  transform: scaleX(0);
  transition: all 0.5s ease 0s;
  width: 25%;
  z-index: 1;
}
.blog-card-two__hover__box--1 {
  left: 0;
  transition-delay: 0.105s;
}
.blog-card-two__hover__box--2 {
  left: 25%;
  transition-delay: 0.105s;
}
.blog-card-two__hover__box--3 {
  left: 50%;
  transition-delay: 0.105s;
}
.blog-card-two__hover__box--4 {
  left: 75%;
  transition-delay: 0s;
}
.blog-card-two__date {
  text-align: center;
  position: absolute;
  top: 30px;
  left: 30px;
  background: var(--wallox-white, #fff);
  font-style: normal;
  font-weight: 700;
  font-size: 14px;
  line-height: 18px;
  margin-bottom: 0;
  padding-bottom: 0;
  color: var(--wallox-text-dark, #2E2A20);
  text-transform: uppercase;
  display: inline-block;
  border-radius: 10px;
  transition: all 0.4s ease-in-out;
  z-index: 2;
}
.blog-card-two__date__day {
  padding: 3px 10px 5px 10px;
  color: var(--wallox-white, #fff);
  display: block;
  background: var(--wallox-base, #DF9E42);
  border-radius: 10px;
}
.blog-card-two__date__month {
  padding: 3px 10px 4px 10px;
  display: block;
}
.blog-card-two__content {
  padding: 8px 0px 0px;
  position: relative;
  z-index: 2;
}
.blog-card-two__meta {
  display: flex;
  align-items: center;
  gap: 18px;
  margin-bottom: 20px;
  margin-left: -5px;
}
.blog-card-two__meta__item {
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  line-height: 133%;
  margin-bottom: 0;
  padding-bottom: 0;
  color: var(--wallox-text, #7E7C76);
}
.blog-card-two__meta__item a {
  color: inherit;
}
.blog-card-two__meta__item a:hover {
  color: var(--wallox-base, #DF9E42);
}
.blog-card-two__meta__item i {
  color: var(--wallox-base, #DF9E42);
  margin: 8px;
}
.blog-card-two__title {
  color: var(--wallox-text-dark, #2E2A20);
  margin-bottom: 13px;
  padding-bottom: 0;
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 160%;
  letter-spacing: -0.02em;
  text-transform: capitalize;
}
.blog-card-two__title a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.blog-card-two__title a:hover {
  background-size: 100% 1px;
}
.blog-card-two__title a:hover {
  color: var(--wallox-base, #DF9E42);
}
.blog-card-two__text {
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 175%;
  padding-bottom: 0;
  text-transform: capitalize;
  color: var(--wallox-text, #7E7C76);
  margin-bottom: 24px;
}
.blog-card-two__link {
  display: inline-flex;
  text-transform: uppercase;
  font-style: normal;
  font-weight: 700;
  font-size: 15px;
  line-height: 19px;
  margin-bottom: 0;
  color: var(--wallox-white, #fff);
  padding: 17.5px 22.5px;
  border-radius: 10px;
  background-color: var(--wallox-base, #DF9E42);
}
.blog-card-two__link__icon {
  position: relative;
  display: inline-flex;
  margin-left: 27px;
}
.blog-card-two__link__icon i {
  font-size: 20px;
}
.blog-card-two__link__icon::before {
  content: "";
  width: 1px;
  height: 100%;
  position: absolute;
  top: 50%;
  left: -14px;
  transform: translateY(-50%);
  background-color: rgba(var(--wallox-white-rgb, 255, 255, 255), 0.3);
  transition: all 400ms ease;
}
.blog-card-two__link::before {
  border-radius: 10px;
  background-color: var(--wallox-text-dark, #2E2A20);
}
.blog-card-two__link:hover {
  transform: scale(1);
}
.blog-card-two:hover .blog-card-two__image img {
  transform: scale(1.1);
}
.blog-card-two:hover .blog-card__hover__box {
  opacity: 1;
  transform: scale(1);
}

/*Blog Card Three Style*/
.blog-card-three {
  position: relative;
  transition: all 0.4s ease-in-out;
}
.blog-card-three__image {
  display: block;
  position: relative;
  overflow: hidden;
  border-radius: 20px;
}
.blog-card-three__image img {
  width: 100%;
  object-fit: cover;
  transform: scale(1);
  transition: all 0.5s ease-in-out;
  border-radius: 20px;
}
.blog-card-three__hover__box {
  background-color: rgba(var(--wallox-text-dark-rgb, 46, 42, 32), 0.7);
  height: 100%;
  top: 0;
  opacity: 0;
  position: absolute;
  transform: scaleX(0);
  transition: all 0.5s ease 0s;
  width: 25%;
  z-index: 1;
}
.blog-card-three__hover__box--1 {
  left: 0;
  transition-delay: 0.105s;
}
.blog-card-three__hover__box--2 {
  left: 25%;
  transition-delay: 0.105s;
}
.blog-card-three__hover__box--3 {
  left: 50%;
  transition-delay: 0.105s;
}
.blog-card-three__hover__box--4 {
  left: 75%;
  transition-delay: 0s;
}
.blog-card-three__date {
  text-align: center;
  position: absolute;
  top: 20px;
  z-index: 1;
  left: 20px;
  background: var(--wallox-white, #fff);
  font-style: normal;
  font-weight: 700;
  font-size: 14px;
  line-height: 18px;
  margin-bottom: 0;
  padding-bottom: 0;
  color: var(--wallox-text-dark, #2E2A20);
  text-transform: uppercase;
  display: inline-block;
  border-radius: 10px;
  transition: all 0.4s ease-in-out;
}
.blog-card-three__date__day {
  padding: 3px 10px 5px 10px;
  color: var(--wallox-white, #fff);
  display: block;
  background: var(--wallox-base, #DF9E42);
  border-radius: 10px 10px 0 0;
}
.blog-card-three__date__month {
  padding: 3px 10px 4px 10px;
  display: block;
}
.blog-card-three__content {
  position: relative;
  z-index: 1;
  margin-top: -95px;
  padding: 25px 30px 25px;
  border-radius: 20px;
  background: var(--wallox-gray, #F4EDE4);
  margin-left: 20px;
}
.blog-card-three__title {
  margin-top: -2px;
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 160%;
  margin-bottom: 10px;
  padding-bottom: 0;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
}
@media (max-width: 1400px) and (min-width: 1200px) {
  .blog-card-three__title {
    font-size: 17px;
  }
}
@media (max-width: 767px) and (min-width: 575px) {
  .blog-card-three__title {
    font-size: 16px;
  }
}
.blog-card-three__title a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.blog-card-three__title a:hover {
  background-size: 100% 1px;
}
.blog-card-three__title a:hover {
  color: var(--wallox-base, #DF9E42);
}
.blog-card-three__meta {
  display: flex;
  align-items: center;
  gap: 18px;
  margin-bottom: 4px;
  margin-left: -5px;
}
.blog-card-three__meta__item {
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  line-height: 133%;
  margin-bottom: 0;
  padding-bottom: 0;
  color: var(--wallox-text, #7E7C76);
}
.blog-card-three__meta__item a {
  color: inherit;
}
.blog-card-three__meta__item a:hover {
  color: var(--wallox-base, #DF9E42);
}
.blog-card-three__meta__item i {
  color: var(--wallox-base, #DF9E42);
  margin: 8px;
}
.blog-card-three:hover .blog-card-three__image img {
  transform: scale(1.1);
}
.blog-card-three:hover .blog-card-three__hover__box {
  opacity: 1;
  transform: scale(1);
}

.blog-one {
  padding: 120px 0;
  position: relative;
}
@media (max-width: 991px) {
  .blog-one {
    padding: 100px 0;
  }
}
@media (max-width: 767px) {
  .blog-one {
    padding: 80px 0;
  }
}
@media (min-width: 992px) {
  .blog-one__carousel .owl-nav {
    display: none;
  }
}
.blog-one--page .post-pagination {
  margin-top: 30px;
}
.blog-one--home {
  background-color: var(--wallox-gray, #F4EDE4);
  position: relative;
  z-index: 1;
}
.blog-one__bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  opacity: 0.3;
  z-index: -1;
}
.blog-one__shape {
  position: absolute;
  right: 0;
  bottom: 10%;
  animation: rotated 20s infinite linear;
  z-index: -1;
}
@media (max-width: 991px) {
  .blog-one__shape {
    display: none;
  }
}
.blog-one__shape-two {
  position: absolute;
  left: 0;
  top: 10%;
  animation: topToBottom 3s ease-in-out infinite;
  animation-delay: 1s;
  z-index: -1;
}
@media (max-width: 991px) {
  .blog-one__shape-two {
    display: none;
  }
}

/*Blog two*/
.blog-two {
  position: relative;
  padding: 120px 0;
  z-index: 1;
}
@media (max-width: 991px) {
  .blog-two {
    padding: 100px 0;
  }
}
@media (max-width: 767px) {
  .blog-two {
    padding: 80px 0;
  }
}
.blog-two .container {
  max-width: 1600px;
  margin-left: auto;
  margin-right: auto;
}

/*Blog Three*/
.blog-three {
  position: relative;
  padding: 120px 0 100px;
  background-color: var(--wallox-gray, #F4EDE4);
  z-index: 1;
}
@media (max-width: 991px) {
  .blog-three {
    padding: 100px 0 100px;
  }
}
@media (max-width: 767px) {
  .blog-three {
    padding: 80px 0 80px;
  }
}
.blog-three--three {
  position: relative;
  z-index: 1;
  margin-top: -154px;
  padding-top: 270px;
}
@media (max-width: 991px) {
  .blog-three--three {
    padding-top: 255px;
  }
}
@media (max-width: 767px) {
  .blog-three--three {
    padding-top: 235px;
  }
}
.blog-three__bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-repeat: no-repeat;
  background-size: cover;
  opacity: 0.05;
  mix-blend-mode: multiply;
}
.blog-three__left {
  border-radius: 20px;
  position: relative;
  background-color: var(--wallox-white, #fff);
  display: flex;
  align-items: center;
  overflow: hidden;
  z-index: 1;
}
@media (max-width: 767px) {
  .blog-three__left {
    flex-direction: column;
  }
}
.blog-three__left__bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-repeat: no-repeat;
  background-size: cover;
  opacity: 0.5;
  mix-blend-mode: multiply;
  z-index: -1;
}
.blog-three__inner {
  padding: 30px;
  flex: 60%;
}
@media (max-width: 1199px) and (min-width: 991px) {
  .blog-three__inner {
    flex: 100%;
  }
}
@media (max-width: 767px) {
  .blog-three__inner {
    flex: 100%;
  }
}
.blog-three__card__item__thumb {
  flex: 37%;
}
@media (max-width: 1199px) and (min-width: 991px) {
  .blog-three__card__item__thumb {
    display: none;
  }
}
@media (max-width: 767px) {
  .blog-three__card__item__thumb {
    flex: 100%;
    display: none;
  }
}
.blog-three__card__item__thumb img {
  width: 100%;
  object-fit: cover;
}
.blog-three__card__item + .blog-three__card__item {
  border-top: 1px solid var(--wallox-border-color, #E4DACC);
  padding-top: 25px;
  margin-top: 20px;
}
.blog-three .blog-card__date {
  background-color: var(--wallox-gray, #F4EDE4);
  margin-bottom: 22px;
}
.blog-three .blog-card__date__day {
  border-radius: 10px 10px 0 0;
}
.blog-three .blog-card__title {
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 160%;
}
.blog-three .blog-card__meta {
  margin-bottom: 0;
}
.blog-three__item {
  border-radius: 20px;
  position: relative;
  background-color: var(--wallox-white, #fff);
  overflow: hidden;
  z-index: 1;
  padding: 30px;
}
.blog-three__item__bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-repeat: no-repeat;
  background-size: cover;
  opacity: 0.5;
  mix-blend-mode: multiply;
  z-index: -1;
}
.blog-three__item__image {
  display: block;
  position: relative;
  overflow: hidden;
  border-radius: 20px;
  margin-bottom: 22px;
}
.blog-three__item__image img {
  width: 100%;
  object-fit: cover;
  transform: scale(1);
  transition: all 0.5s ease-in-out;
}
.blog-three__item__hover__box {
  background-color: rgba(var(--wallox-base-rgb, 223, 158, 66), 0.7);
  height: 100%;
  top: 0;
  opacity: 0;
  position: absolute;
  transform: scaleX(0);
  transition: all 0.5s ease 0s;
  width: 25%;
  z-index: 1;
}
.blog-three__item__hover__box--1 {
  left: 0;
  transition-delay: 0.105s;
}
.blog-three__item__hover__box--2 {
  left: 25%;
  transition-delay: 0.105s;
}
.blog-three__item__hover__box--3 {
  left: 50%;
  transition-delay: 0.105s;
}
.blog-three__item__hover__box--4 {
  left: 75%;
  transition-delay: 0s;
}
.blog-three__item__date {
  text-align: center;
  position: absolute;
  top: 30px;
  z-index: 1;
  left: 30px;
  background: var(--wallox-white, #fff);
  font-style: normal;
  font-weight: 700;
  font-size: 14px;
  line-height: 18px;
  margin-bottom: 0;
  padding-bottom: 0;
  color: var(--wallox-text-dark, #2E2A20);
  text-transform: uppercase;
  display: inline-block;
  border-radius: 10px;
  transition: all 0.4s ease-in-out;
}
.blog-three__item__date__day {
  padding: 3px 10px 5px 10px;
  color: var(--wallox-white, #fff);
  display: block;
  background: var(--wallox-base, #DF9E42);
  border-radius: 10px 10px 0 0;
}
.blog-three__item__date__month {
  padding: 3px 10px 4px 10px;
  display: block;
}
.blog-three__item__title {
  color: var(--wallox-text-dark, #2E2A20);
  padding-bottom: 0;
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 160%;
  letter-spacing: -0.02em;
  text-transform: capitalize;
  margin-bottom: 13px;
}
@media (max-width: 1199px) and (min-width: 992px) {
  .blog-three__item__title {
    font-size: 18px;
  }
}
@media (max-width: 400px) {
  .blog-three__item__title {
    font-size: 18px;
  }
}
.blog-three__item__title a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.blog-three__item__title a:hover {
  background-size: 100% 1px;
}
.blog-three__item__title a:hover {
  color: var(--wallox-base, #DF9E42);
}
.blog-three__item__meta {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 0px;
}
.blog-three__item__meta__item {
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  line-height: 133%;
  margin-bottom: 0;
  padding-bottom: 0;
  color: var(--wallox-text, #7E7C76);
}
.blog-three__item__meta__item a {
  color: inherit;
}
.blog-three__item__meta__item a:hover {
  color: var(--wallox-base, #DF9E42);
}
.blog-three__item__meta__item i {
  color: var(--wallox-base, #DF9E42);
  margin-right: 5px;
}
.blog-three__item:hover {
  background-color: var(--wallox-white, #fff);
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
}
.blog-three__item:hover .blog-three__item__image img {
  transform: scale(1.1);
}
.blog-three__item:hover .blog-three__item__date {
  background: var(--wallox-gray, #F4EDE4);
}
.blog-three__item:hover .blog-three__item__hover__box {
  opacity: 1;
  transform: scale(1);
}
.blog-three .client-carousel {
  padding-top: 100px;
}
@media (max-width: 991px) {
  .blog-three .client-carousel {
    padding: 100px 0 0px;
  }
}
@media (max-width: 767px) {
  .blog-three .client-carousel {
    padding: 80px 0 0px;
  }
}

/*--------------------------------------------------------------
# Form
--------------------------------------------------------------*/
.form-one__group {
  display: grid;
  grid-template-columns: 1fr;
  grid-gap: 20px;
  margin: 0;
}
@media (min-width: 576px) {
  .form-one__group {
    grid-template-columns: repeat(2, 1fr);
  }
}
.form-one__control {
  border: none;
  width: auto;
  height: auto;
  border-radius: 0;
  padding: 0;
  position: relative;
}
.form-one__control__icon {
  position: absolute;
  top: 50%;
  right: 30px;
  transform: translateY(-50%);
  font-size: 14px;
}
.form-one__control--full {
  grid-column-start: 1;
  grid-column-end: -1;
}
.form-one .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
  width: 100%;
  height: 58px;
  display: flex;
  align-items: center;
}
.form-one .bootstrap-select > .dropdown-toggle {
  padding: 0;
  background-color: transparent;
  border-radius: 0;
  border: none;
  outline: none !important;
  color: var(--wallox-text, #7E7C76);
  font-size: 14px;
}
.form-one .bootstrap-select > .dropdown-toggle,
.form-one input[type=text],
.form-one input[type=email],
.form-one textarea {
  display: block;
  width: 100%;
  height: 58px;
  background-color: var(--wallox-gray, #F4EDE4);
  color: var(--wallox-text, #7E7C76);
  font-size: 14px;
  font-weight: 500;
  border: none;
  outline: none;
  padding-left: 30px;
  padding-right: 30px;
}
.form-one textarea {
  height: 195px;
  padding-top: 20px;
}
.form-one .bootstrap-select > .dropdown-toggle {
  display: flex;
  align-items: center;
}
.form-one .bootstrap-select > .dropdown-toggle .filter-option {
  display: flex;
  align-items: center;
}

/*--------------------------------------------------------------
# Custom Cursor
--------------------------------------------------------------*/
.custom-cursor__cursor {
  width: 25px;
  height: 25px;
  border-radius: 100%;
  border: 1px solid var(--wallox-base, #DF9E42);
  -webkit-transition: all 200ms ease-out;
  transition: all 200ms ease-out;
  position: fixed;
  pointer-events: none;
  left: 0;
  top: 0;
  -webkit-transform: translate(calc(-50% + 5px), -50%);
  transform: translate(calc(-50% + 5px), -50%);
  z-index: 999991;
}
.custom-cursor__cursor-two {
  width: 10px;
  height: 10px;
  border-radius: 100%;
  background-color: var(--wallox-base, #DF9E42);
  opacity: 0.3;
  position: fixed;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  pointer-events: none;
  -webkit-transition: width 0.3s, height 0.3s, opacity 0.3s;
  transition: width 0.3s, height 0.3s, opacity 0.3s;
  z-index: 999991;
}
.custom-cursor__hover {
  background-color: var(--wallox-base, #DF9E42);
  opacity: 0.4;
}
.custom-cursor__innerhover {
  width: 25px;
  height: 25px;
  opacity: 0.4;
}

/*--------------------------------------------------------------
# Footer
--------------------------------------------------------------*/
.main-footer {
  position: relative;
  background-color: var(--wallox-text-dark, #2E2A20);
}
.main-footer__bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  mix-blend-mode: darken;
  opacity: 0.1;
  background-size: cover;
  background-position: center center;
}
.main-footer__top__inner {
  padding-top: 120px;
  display: flex;
  align-self: center;
  gap: 50px;
  padding-bottom: 60px;
  border-bottom: 1px solid rgba(var(--wallox-border-color-rgb, 228, 218, 204), 0.2);
}
@media (max-width: 991px) {
  .main-footer__top__inner {
    padding-top: 100px;
    flex-direction: column;
    justify-content: start;
    align-items: start;
    gap: 30px;
  }
}
@media (max-width: 767px) {
  .main-footer__top__inner {
    padding-top: 80px;
  }
}
.main-footer__logo .footer-logo {
  display: flex;
}
.main-footer__form {
  width: 100%;
  margin-left: 70px;
}
@media (max-width: 991px) {
  .main-footer__form {
    margin-left: 0px;
  }
}
.main-footer__form__newsletter {
  display: flex;
  position: relative;
  width: 100%;
  background-color: var(--wallox-white, #fff);
  border-radius: 10px;
}
.main-footer__form__newsletter__icon {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 22px;
  color: var(--wallox-text, #7E7C76);
}
.main-footer__form__newsletter input[type=text] {
  border-radius: 10px 11px 11px 10px;
  width: 100%;
  display: block;
  border: none;
  outline: none;
  height: 55px;
  background-color: var(--wallox-white, #fff);
  color: var(--wallox-text, #7E7C76);
  font-size: 16px;
  font-weight: 500;
  padding-left: 60px;
  padding-right: 20px;
  transition: all 500ms ease;
  margin-bottom: 1px;
}
.main-footer__form__newsletter input[type=text]:focus {
  color: var(--wallox-text, #7E7C76);
}
.main-footer__form__newsletter input[type=text]::placeholder {
  font-style: normal;
  font-weight: 400;
  font-size: 15px;
  line-height: 19px;
  color: var(--wallox-text, #7E7C76);
  text-transform: capitalize;
}
.main-footer__form__newsletter button[type=submit] {
  background-color: var(--wallox-base, #DF9E42);
  width: auto;
  height: 100%;
  border: none;
  outline: none;
  position: absolute;
  top: 50%;
  right: -1px;
  margin-bottom: -1px;
  transform: translateY(-50%);
  transition: all 500ms ease;
  border-radius: 10px;
  font-weight: 700;
}
.main-footer__form__newsletter button[type=submit]:hover {
  background-color: var(--wallox-text-dark, #2E2A20);
  color: var(--wallox-white, #fff);
}
.main-footer__form__newsletter button[type=submit]::before {
  display: none;
}
.main-footer__social {
  display: flex;
  align-items: center;
  gap: 10px;
}
.main-footer__social a {
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid rgba(var(--wallox-border-color-rgb, 228, 218, 204), 0.2);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  font-size: 14px;
  color: var(--wallox-text, #7E7C76);
}
.main-footer__social a:hover {
  color: var(--wallox-white, #fff);
  border: 1px solid rgba(var(--wallox-base-rgb, 223, 158, 66), 0.2);
  background: var(--wallox-base, #DF9E42);
}
.main-footer .container {
  position: relative;
}
.main-footer__middle {
  padding-top: 50px;
  padding-bottom: 50px;
}
@media (max-width: 1199px) {
  .main-footer__middle {
    padding-bottom: 20px;
  }
}
.main-footer__bottom {
  text-align: center;
}
.main-footer__bottom__inner {
  padding: 38px 0;
  border-top: 1px solid rgba(var(--wallox-white-rgb, 255, 255, 255), 0.1);
}
.main-footer__copyright {
  margin: 0;
  color: var(--wallox-text, #7E7C76);
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 88%;
  text-align: center;
  text-transform: capitalize;
}

@media (max-width: 1199px) {
  .footer-widget {
    margin-bottom: 30px;
  }
}
.footer-widget__title {
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 1.277;
  color: var(--wallox-white, #fff);
  padding-bottom: 13px;
  position: relative;
  display: inline-block;
  margin-bottom: 30px;
}
.footer-widget__title::after {
  position: absolute;
  bottom: 0;
  left: 0;
  content: "";
  width: 70px;
  height: 1px;
  background: linear-gradient(90deg, rgb(228, 218, 204) 0%, rgba(228, 218, 204, 0) 100%);
}
.footer-widget__links {
  margin-top: -12px;
  margin-left: 0;
  margin-bottom: 0;
}
.footer-widget__links li {
  position: relative;
  padding-left: 15px;
}
.footer-widget__links li a {
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 212%;
  margin-bottom: 0;
  padding-bottom: 0;
  text-transform: capitalize;
  color: var(--wallox-text, #7E7C76);
  position: relative;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.footer-widget__links li a:hover {
  background-size: 100% 1px;
}
.footer-widget__links li a:hover {
  color: var(--wallox-base, #DF9E42);
}
.footer-widget__links li i {
  font-size: 6px;
  position: absolute;
  left: 0;
  top: 40%;
  transform: translateY(-50%);
  transform: scale(1);
  transition: all 0.4s ease-out;
}
.footer-widget__links li:hover i {
  transform: scale(0);
}
.footer-widget__text {
  margin-top: -7px;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 188%;
  padding-bottom: 0;
  text-transform: capitalize;
  color: var(--wallox-text, #7E7C76);
  max-width: 325px;
  width: 100%;
  margin-bottom: 30px;
}
.footer-widget__info li {
  display: flex;
  align-items: start;
  gap: 8px;
}
.footer-widget__info li i {
  font-size: 15px;
  color: var(--wallox-base, #DF9E42);
}
.footer-widget__info li + li {
  margin-top: 24px;
}
.footer-widget__contact__text, .footer-widget__contact__link {
  margin-top: -5px;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 160%;
  margin-bottom: 0;
  padding-bottom: 0;
  color: var(--wallox-text, #7E7C76);
  transition: all 0.4s ease-in-out;
}
.footer-widget__contact__link {
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.footer-widget__contact__link:hover {
  background-size: 100% 1px;
}
.footer-widget__contact__link:hover {
  color: var(--wallox-base, #DF9E42);
}
.footer-widget__btn {
  color: var(--wallox-base, #DF9E42);
}
.footer-widget__btn:hover {
  color: var(--wallox-white, #fff);
}
.footer-widget--about .wallox-btn {
  font-style: normal;
  font-weight: 700;
  font-size: 15px;
  line-height: 19px;
  text-transform: capitalize;
  color: var(--wallox-base, #DF9E42);
  border: 1px solid rgba(228, 218, 204, 0.15);
  background-color: transparent;
  padding: 11px 14px;
}
.footer-widget--about .wallox-btn i {
  font-size: 12px;
  margin-left: 7px;
}
.footer-widget--about .wallox-btn:hover {
  color: var(--wallox-white, #fff);
}
.footer-widget--links {
  margin-left: 39px;
}
@media (max-width: 1199px) {
  .footer-widget--links {
    margin-left: 0;
  }
}
.footer-widget--service {
  margin-left: -30px;
}
@media (max-width: 1199px) {
  .footer-widget--service {
    margin-left: 0;
  }
}
/*--------------------------------------------------------------
# Contact
--------------------------------------------------------------*/
.contact-one {
  padding: 120px 0px;
}
@media (max-width: 991px) {
  .contact-one {
    padding: 100px 0px;
  }
}
@media (max-width: 767px) {
  .contact-one {
    padding: 80px 0px;
  }
}
.contact-one__item + .contact-one__item {
  margin-top: 30px;
}
.contact-one__item {
  background: var(--wallox-gray, #F4EDE4);
  display: flex;
  gap: 20px;
  align-items: start;
  padding: 37px 20px 37px 30px;
  border: 1px solid var(--wallox-border-color, #E4DACC);
  border-radius: 20px;
  transition: all 0.4s ease-in-out;
}
@media (max-width: 575px) {
  .contact-one__item {
    padding: 30px 15px 30px 20px;
  }
}
.contact-one__item__icon {
  max-width: 84px;
  width: 100%;
  height: 84px;
  border-radius: 50%;
  background: var(--wallox-white, #fff);
  border: 10px solid var(--wallox-border-color, #E4DACC);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30px;
  color: var(--wallox-base, #DF9E42);
  transition: all 0.4s ease-in-out;
  position: relative;
  z-index: 1;
}
@media (max-width: 575px) {
  .contact-one__item__icon {
    max-width: 70px;
    height: 70px;
  }
}
.contact-one__item__icon::after {
  content: "";
  position: absolute;
  top: 0%;
  height: 0;
  width: 0;
  left: 50%;
  background-color: var(--wallox-base, #DF9E42);
  border-radius: 50%;
  transition: all 0.4s ease;
  z-index: -1;
}
.contact-one__item__title {
  font-style: normal;
  font-weight: 600;
  font-size: 20px;
  line-height: 80%;
  margin-bottom: 11px;
  padding-bottom: 0;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
  text-shadow: 0 0 0.1px currentColor;
}
.contact-one__item__call, .contact-one__item__text {
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 160%;
  margin-bottom: 0;
  color: var(--wallox-text, #7E7C76);
  padding-bottom: 0;
  display: block;
}
.contact-one__item__call {
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.contact-one__item__call:hover {
  background-size: 100% 1px;
}
.contact-one__item__call:hover {
  color: var(--wallox-base, #DF9E42);
}
.contact-one__item:hover .contact-one__item__icon {
  color: var(--wallox-white, #fff);
}
.contact-one__item:hover .contact-one__item__icon i {
  animation: top-bottom 0.4s ease-in-out;
}
.contact-one__item:hover .contact-one__item__icon::after {
  top: 0%;
  height: 100%;
  width: 100%;
  left: 0%;
}
.contact-one__item:hover {
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
  background: var(--wallox-white, #fff);
  border-color: transparent;
}
.contact-one__inner {
  position: relative;
  background: var(--wallox-gray, #F4EDE4);
  padding: 30px;
  overflow: hidden;
  border-radius: 20px;
}
.contact-one__bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-repeat: no-repeat;
  background-size: cover;
  opacity: 0.15;
}
.contact-one__form__thumb {
  position: relative;
  z-index: 1;
  margin-right: 12px;
}
@media (max-width: 991px) {
  .contact-one__form__thumb {
    margin-right: 0;
    margin-bottom: 30px;
  }
}
@media (max-width: 767px) {
  .contact-one__form__thumb {
    display: none;
  }
}
.contact-one__form__thumb img {
  object-fit: cover;
  width: 100%;
  border-radius: 10px;
}
.contact-one__form {
  margin-left: -12px;
}
@media (max-width: 991px) {
  .contact-one__form {
    margin-left: 0px;
  }
}
.contact-one__form input[type=text],
.contact-one__form input[type=email] {
  border-radius: 10px;
  height: 60px;
  width: 100%;
  border: none;
  background-color: var(--wallox-white, #fff);
  padding-left: 20px;
  padding-right: 20px;
  outline: none;
  font-weight: 400;
  font-size: 16px;
  color: var(--wallox-text, #7E7C76);
  display: block;
}
.contact-one__form input[type=text]::placeholder,
.contact-one__form input[type=email]::placeholder {
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 160%;
  color: var(--wallox-text, #7E7C76);
  text-transform: capitalize;
}
.contact-one__form textarea {
  border-radius: 10px;
  height: 150px;
  width: 100%;
  border: none;
  background-color: var(--wallox-white, #fff);
  padding-left: 20px;
  padding-right: 20px;
  outline: none;
  font-weight: 400;
  font-size: 16px;
  color: var(--wallox-text, #7E7C76);
  display: block;
}
.contact-one__form textarea::placeholder {
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 160%;
  color: var(--wallox-text, #7E7C76);
  text-transform: capitalize;
}
.contact-one__form .wallox-btn__submite {
  display: flex;
  align-items: center;
}
.contact-one .wallox-btn::before {
  border-radius: 10px;
}
.contact-one .wallox-btn__submite {
  border-radius: 10px;
  color: var(--wallox-white, #fff);
}
.contact-one .wallox-btn__submite i {
  margin-left: 20px;
  font-size: 14px;
}

/*--------------------------------------------------------------
# Topbar
--------------------------------------------------------------*/
/**Top Bar One**/
.topbar-one {
  position: relative;
  z-index: 1;
  background: var(--wallox-text-dark, #2E2A20);
}
@media (max-width: 991px) {
  .topbar-one {
    display: none;
  }
}
.topbar-one__inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0px;
  max-width: 1520px;
  margin-left: auto;
  margin-right: auto;
}
.topbar-one__info {
  display: flex;
  gap: 30px;
  align-items: center;
  margin-bottom: 0;
}
.topbar-one__info__item {
  display: flex;
  align-self: center;
  gap: 10px;
}
.topbar-one__info__icon {
  font-size: 14px;
  color: var(--wallox-base, #DF9E42);
  line-height: 1.6;
}
.topbar-one__info__icon i {
  line-height: 1;
}
.topbar-one__info__content, .topbar-one__info__location {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 1.25;
  color: var(--wallox-white, #fff);
  margin-bottom: 0;
}
.topbar-one__info__content {
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.topbar-one__info__content:hover {
  background-size: 100% 1px;
}
.topbar-one__info__content:hover {
  color: var(--wallox-base, #DF9E42);
}
.topbar-one__social {
  display: flex;
  align-items: center;
  gap: 10px;
}
.topbar-one__social a {
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid rgba(var(--wallox-border-color-rgb, 228, 218, 204), 0.2);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  font-size: 14px;
  color: var(--wallox-text, #7E7C76);
}
.topbar-one__social a:hover {
  color: var(--wallox-white, #fff);
  border: 1px solid rgba(var(--wallox-base-rgb, 223, 158, 66), 0.2);
  background: var(--wallox-base, #DF9E42);
}

/**Top Bar Two**/
@media (max-width: 1199px) {
  .topbar-two {
    padding: 10px 0px;
  }
}
@media (max-width: 991px) {
  .topbar-two {
    display: none;
  }
}
.topbar-two .container-fluid {
  max-width: 1550px;
  margin-left: auto;
  margin-right: auto;
}
.topbar-two__inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.topbar-two__logo {
  position: relative;
}
@media (max-width: 1199px) {
  .topbar-two__logo {
    display: none;
  }
}
.topbar-two__logo a {
  padding: 29px 100px 29px 0px;
  background: var(--wallox-gray, #F4EDE4);
  clip-path: polygon(0 0, 100% 0, 88% 100%, 0% 100%);
  display: flex;
  align-items: center;
  justify-content: start;
}
.topbar-two__logo::after {
  content: "";
  background-color: var(--wallox-gray, #F4EDE4);
  width: 999px;
  height: 100%;
  position: absolute;
  top: 0;
  right: 99%;
  z-index: -2;
}
.topbar-two__logo::before {
  content: "";
  background-color: var(--wallox-base, #DF9E42);
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 10px;
  clip-path: polygon(0 0, 100% 0, 84.5% 100%, 0% 100%);
  z-index: -2;
}
@media (max-width: 767px) {
  .topbar-two__logo::before {
    display: none;
  }
}
.topbar-two__list {
  margin-left: 20px;
  margin-right: auto;
}
.topbar-two__info {
  display: flex;
  width: 100%;
  align-items: center;
  gap: 60px;
  margin-bottom: 0;
}
.topbar-two__info__item {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 18px;
}
.topbar-two__info__item:hover .topbar-two__info__icon {
  color: var(--wallox-white, #fff);
}
.topbar-two__info__item:hover .topbar-two__info__icon i {
  animation: top-bottom 0.4s ease-in-out;
}
.topbar-two__info__item:hover .topbar-two__info__icon::after {
  top: 0%;
  height: 100%;
  width: 100%;
  left: 0%;
}
.topbar-two__info__icon {
  max-width: 37px;
  width: 100%;
  height: 37px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--wallox-gray, #F4EDE4);
  font-size: 16px;
  color: var(--wallox-base, #DF9E42);
  position: relative;
  z-index: 1;
}
.topbar-two__info__icon::after {
  content: "";
  position: absolute;
  top: 0%;
  height: 0;
  width: 0;
  left: 50%;
  background-color: var(--wallox-base, #DF9E42);
  border-radius: 50%;
  transition: all 0.4s ease;
  z-index: -1;
}
.topbar-two__info__subtitle {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  color: var(--wallox-text, #7E7C76);
  display: block;
  text-transform: capitalize;
}
.topbar-two__info__location, .topbar-two__info__content {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  color: var(--wallox-text-dark, #2E2A20);
  margin-bottom: 0;
  padding-bottom: 0;
  display: block;
}
.topbar-two__info__location {
  display: block;
}
.topbar-two__social {
  display: flex;
  align-items: center;
  gap: 10px;
}
.topbar-two__social a {
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid var(--wallox-border-color, #E4DACC);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  font-size: 14px;
  color: var(--wallox-text, #7E7C76);
}
.topbar-two__social a:hover {
  color: var(--wallox-white, #fff);
  border: 1px solid rgba(var(--wallox-base-rgb, 223, 158, 66), 0.2);
  background: var(--wallox-base, #DF9E42);
}

/*--------------------------------------------------------------
# Navigations
--------------------------------------------------------------*/
.main-header {
  background: var(--wallox-white, #fff);
}
.main-header .container-fluid {
  max-width: 1550px;
}
.main-header__inner {
  display: flex;
  align-items: center;
  padding: 20px 0;
  position: relative;
}
@media (max-width: 767px) {
  .main-header__inner {
    padding: 10px 0;
  }
}
.main-header__logo {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
}
@media (min-width: 768px) {
  .main-header__logo {
    width: auto;
  }
}
@media (min-width: 768px) {
  .main-header__logo .mobile-nav__btn {
    margin-left: 30px;
  }
}
.main-header__right {
  display: flex;
  align-items: center;
}
.main-header__right__info {
  display: flex;
  align-items: center;
}
.main-header__right__info__item {
  font-size: 16px;
  color: var(--wallox-text-dark, #2E2A20);
  font-weight: 900;
}
.main-header__right__info__item:hover {
  color: var(--wallox-base, #DF9E42);
}
.main-header__right__info__item + .main-header__right__info__item {
  margin-left: 40px;
}
@media (max-width: 575px) {
  .main-header__right__info__item + .main-header__right__info__item {
    margin-left: 20px;
  }
}
@media (max-width: 1300px) and (min-width: 1199px) {
  .main-header__right__info__item + .main-header__right__info__item {
    margin-left: 25px;
  }
}
.main-header__right__info + .main-header__right__link {
  margin-left: 60px;
}
@media (max-width: 1300px) and (min-width: 1199px) {
  .main-header__right__info + .main-header__right__link {
    margin-left: 30px;
  }
}
.main-header__right__link {
  padding: 24px 0px 24px 24px;
  border-radius: 100px 0px 0px 100px;
  background-color: var(--wallox-base, #DF9E42);
  position: relative;
  z-index: 1;
}
@media (max-width: 767px) {
  .main-header__right__link {
    display: none;
  }
}
.main-header__right__link::after {
  content: "";
  background-color: var(--wallox-base, #DF9E42);
  width: 999px;
  height: 100%;
  position: absolute;
  top: 0;
  left: 99%;
  z-index: -2;
}
@media (max-width: 767px) {
  .main-header__right__link::after {
    display: none;
  }
}
.main-header__right__link::before {
  content: "";
  background: url(../images/shapes/menu-bg.png);
  background-repeat: no-repeat;
  background-size: cover;
  width: 410px;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0%;
  z-index: -1;
  mix-blend-mode: multiply;
  transform: matrix(1, 0, 0, -1, 0, 0);
  opacity: 0.3;
}
@media (max-width: 767px) {
  .main-header__right__link::before {
    display: none;
  }
}
.main-header__right__btn {
  padding: 14px 20px;
  font-weight: 600;
}
.main-header__right__btn::before {
  background-color: var(--wallox-white, #fff);
}
.main-header__right__btn:hover {
  color: var(--wallox-text-dark, #2E2A20);
}
.main-header__nav {
  margin-left: auto;
  margin-right: 60px;
}
@media (max-width: 575px) {
  .main-header__nav {
    margin-right: 0px;
  }
}
.main-header--two {
  background: var(--wallox-text-dark, #2E2A20);
}
.main-header--two .main-header__right__link::before, .main-header--two .main-header__right__link::after {
  display: none;
}
.main-header--two .main-header__logo {
  position: relative;
  z-index: 1;
}
.main-header--two .main-header__logo a {
  background: var(--wallox-base, #DF9E42);
  padding: 30px 71px 30px 0px;
  clip-path: polygon(80% 0, 100% 45%, 100% 100%, 0 100%, 0 0);
}
@media (max-width: 991px) {
  .main-header--two .main-header__logo a {
    padding: 10px 30px 10px 0px;
  }
}
.main-header--two .main-header__logo::after {
  content: "";
  background-color: var(--wallox-base, #DF9E42);
  width: 999px;
  height: 100%;
  position: absolute;
  top: 0;
  right: 99%;
  z-index: -2;
}
.main-header--two .main-header__logo::before {
  content: "";
  background-color: var(--wallox-white, #fff);
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 10px;
  clip-path: polygon(80% 0, 100% 45%, 100% 100%, 0 100%, 0 0);
  z-index: -2;
}
@media (max-width: 767px) {
  .main-header--two .main-header__logo::before {
    display: none;
  }
}
.main-header--two .mobile-nav__btn span {
  background-color: var(--wallox-white, #fff);
}
.main-header--two .main-header__inner {
  padding: 0 0;
}
.main-header--two .main-menu .main-menu__list > li {
  padding-top: 45px;
  padding-bottom: 45px;
}
.main-header--two .main-header__right__link {
  padding: 0;
  margin: 0;
  background: transparent;
}
.main-header--two .main-menu .main-menu__list > li > a,
.main-header--two .main-header__right__info__item {
  color: var(--wallox-white, #fff);
}
@media (max-width: 1420px) {
  .main-header--two .main-menu .main-menu__list > li + li {
    margin-left: 20px;
  }
}
@media (max-width: 1420px) {
  .main-header--two .main-header__nav {
    margin-right: 40px;
  }
}
@media (max-width: 575px) {
  .main-header--two .main-header__nav {
    margin-right: 0px;
  }
}
.main-header--two .main-header__right__bar {
  font-size: 16px;
  color: var(--wallox-base, #DF9E42);
  margin-left: 60px;
  cursor: pointer;
}
@media (max-width: 1420px) {
  .main-header--two .main-header__right__bar {
    margin-left: 40px;
  }
}
@media (max-width: 1199px) {
  .main-header--two .main-header__right__bar {
    display: none;
  }
}
.main-header--two .main-header__right__btn {
  margin-left: 60px;
}
@media (max-width: 1500px) and (min-width: 1199px) {
  .main-header--two .main-header__right__btn {
    display: none;
  }
}
@media (max-width: 1420px) {
  .main-header--two .main-header__right__btn {
    margin-left: 40px;
  }
}
.main-header--three {
  background-color: var(--wallox-text-dark, #2E2A20);
}
.main-header--three .main-menu .main-menu__list > li {
  padding-top: 27.25px;
  padding-bottom: 27.25px;
}
.main-header--three .main-header__inner {
  padding: 0;
}
@media (max-width: 1199px) {
  .main-header--three .main-header__inner {
    padding: 10px 0px;
  }
}
.main-header--three .mobile-nav__btn span {
  background-color: var(--wallox-white, #fff);
}
.main-header--three .main-header__right__link {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
}
.main-header--three .main-header__right__link::after, .main-header--three .main-header__right__link::before {
  display: none;
}
.main-header--three .main-header__nav {
  margin-left: 0;
  margin-right: auto;
}
@media (max-width: 1199px) {
  .main-header--three .main-header__nav {
    margin-left: 70px;
  }
}
@media (max-width: 575px) {
  .main-header--three .main-header__nav {
    margin-left: 0px;
  }
}
.main-header--three .main-header__logo {
  display: none;
}
@media (max-width: 1199px) {
  .main-header--three .main-header__logo {
    display: block;
  }
}
.main-header--three .main-menu .main-menu__list > li > a,
.main-header--three .main-header__right__info__item {
  color: var(--wallox-white, #fff);
}

.sticky-header--cloned {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  top: 0;
  background-color: var(--wallox-white, #fff);
  transform: translateY(-100%);
  box-shadow: 0px 3px 18px rgba(var(--wallox-text-dark-rgb, 46, 42, 32), 0.07);
  transition: 0.6s cubic-bezier(0.24, 0.74, 0.58, 1);
  visibility: hidden;
  transition: transform 500ms ease, visibility 500ms ease;
}
.sticky-header--cloned.active {
  transform: translateY(0%);
  visibility: visible;
}

.main-header--two.active {
  background: var(--wallox-text-dark, #2E2A20);
}

.main-header--three.active {
  background: var(--wallox-text-dark, #2E2A20);
}

.mobile-nav__btn {
  width: 24px;
  display: flex;
  align-items: center;
  flex-direction: column;
  flex-wrap: wrap;
  cursor: pointer;
  z-index: 3;
}
@media (max-width: 1199px) {
  .mobile-nav__btn {
    margin-left: 40px;
    margin-right: 10px;
  }
}
@media (max-width: 767px) {
  .mobile-nav__btn {
    margin-left: 30px;
    margin-right: 10px;
  }
}
@media (min-width: 1200px) {
  .mobile-nav__btn {
    display: none;
  }
}
.mobile-nav__btn span {
  width: 100%;
  height: 2px;
  background-color: var(--wallox-text-dark, #2E2A20);
}
.mobile-nav__btn span:nth-child(2) {
  margin-top: 4px;
  margin-bottom: 4px;
}

.main-menu {
  /* after third level no menu */
}
.main-menu .main-menu__list,
.main-menu .main-menu__list ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
  align-items: center;
  display: none;
}
@media (min-width: 1200px) {
  .main-menu .main-menu__list,
  .main-menu .main-menu__list ul {
    display: flex;
  }
}
.main-menu .main-menu__list > li {
  padding-top: 36.25px;
  padding-bottom: 36.25px;
  position: relative;
}
.main-menu .main-menu__list > li.dropdown > a {
  position: relative;
}
.main-menu .main-menu__list > li + li {
  margin-left: 40px;
}
@media (max-width: 1400px) {
  .main-menu .main-menu__list > li + li {
    margin-left: 30px;
  }
}
@media (max-width: 1300px) {
  .main-menu .main-menu__list > li + li {
    margin-left: 20px;
  }
}
.main-menu .main-menu__list > li > a {
  display: flex;
  align-items: center;
  color: var(--wallox-text-dark, #2E2A20);
  text-transform: capitalize;
  position: relative;
  font-size: 14px;
  transition: all 500ms ease;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 1.5;
  text-shadow: 0 0 0.01px currentColor;
}
.main-menu .main-menu__list > li.current > a,
.main-menu .main-menu__list > li:hover > a {
  color: var(--wallox-base, #DF9E42);
  text-shadow: 0 0 0.5px currentColor;
}
.main-menu .main-menu__list li ul {
  position: absolute;
  top: 100%;
  left: -25px;
  min-width: 270px;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  opacity: 0;
  visibility: hidden;
  transform-origin: top center;
  transform: scaleY(0) translateZ(100px);
  transition: opacity 500ms ease, visibility 500ms ease, transform 700ms ease;
  z-index: 99;
  background-color: var(--wallox-white, #fff);
  box-shadow: 0px 10px 60px 0px RGBA(var(--wallox-white-rgb, 255, 255, 255), 0.07);
  padding: 15px 20px 11px;
  box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.1);
}
.main-menu .main-menu__list li:hover > ul {
  opacity: 1;
  visibility: visible;
  transform: scaleY(1) translateZ(0px);
}
.main-menu .main-menu__list > .megamenu {
  position: static;
}
.main-menu .main-menu__list > .megamenu > ul {
  top: 100% !important;
  left: 0 !important;
  right: 0 !important;
  background-color: transparent;
  box-shadow: none;
  padding: 0;
}
.main-menu .main-menu__list li ul li {
  flex: 1 1 100%;
  width: 100%;
  position: relative;
}
.main-menu .main-menu__list li ul li > a {
  font-size: 14px;
  line-height: 26px;
  color: var(--wallox-text-dark, #2E2A20);
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  font-weight: 600;
  display: flex;
  text-transform: capitalize;
  padding: 8px 20px;
  transition: 400ms;
  margin-bottom: 4px;
}
.main-menu .main-menu__list li ul li > a::after {
  position: absolute;
  right: 20px;
  top: 8px;
  border-radius: 0;
  font-size: 6px;
  font-weight: 700;
  font-family: "Font Awesome 5 Free";
  content: "\f111";
  color: var(--wallox-base, #DF9E42);
  visibility: hidden;
  opacity: 0;
  transition: all 500ms ease;
  transform: scale(0);
}
.main-menu .main-menu__list li ul li.current > a,
.main-menu .main-menu__list li ul li:hover > a {
  background-color: var(--wallox-gray, #F4EDE4);
  color: var(--wallox-text-dark, #2E2A20);
}
.main-menu .main-menu__list li ul li.current > a::after,
.main-menu .main-menu__list li ul li:hover > a::after {
  visibility: visible;
  opacity: 1;
  transform: scale(1);
}
.main-menu .main-menu__list li ul li > ul {
  top: 0;
  left: calc(100% + 20px);
}
.main-menu .main-menu__list li ul li > ul.right-align {
  top: 0;
  left: auto;
  right: 100%;
}
.main-menu .main-menu__list li ul li > ul ul {
  display: none;
}

@media (min-width: 1200px) and (max-width: 1400px) {
  .main-menu__list li:nth-last-child(1) ul li > ul,
  .main-menu__list li:nth-last-child(2) ul li > ul {
    left: auto;
    right: calc(100% + 20px);
  }
}
/*--------------------------------------------------------------
# Megamenu Popup
--------------------------------------------------------------*/
.mobile-nav__container .main-menu__list > .megamenu.megamenu-clickable > ul,
.main-menu .main-menu__list > .megamenu.megamenu-clickable > ul,
.stricky-header .main-menu__list > .megamenu.megamenu-clickable > ul {
  position: fixed;
  top: 0 !important;
  left: 0 !important;
  width: 100vw;
  height: 100vh;
  visibility: visible;
  overflow-y: scroll;
  visibility: hidden;
  opacity: 0;
  -webkit-transform: scale(1, 0);
  transform: scale(1, 0);
  -webkit-transform-origin: bottom center;
  transform-origin: bottom center;
  transition: transform 0.7s ease, opacity 0.7s ease, visibility 0.7s ease;
  z-index: 999999;
  -ms-overflow-style: none;
  scrollbar-width: none;
  overflow-y: scroll;
  padding: 0;
  background-color: var(--wallox-white, #fff);
  display: block !important;
  margin: 0;
}

.main-menu__list > li.megamenu-clickable > ul::-webkit-scrollbar {
  display: none;
}

.mobile-nav__container .main-menu__list > .megamenu.megamenu-clickable > ul.megamenu-clickable--active,
.main-menu .main-menu__list > .megamenu.megamenu-clickable > ul.megamenu-clickable--active,
.stricky-header .main-menu__list > .megamenu.megamenu-clickable > ul.megamenu-clickable--active {
  -webkit-transform-origin: top center;
  transform-origin: top center;
  -webkit-transform: scale(1, 1);
  transform: scale(1, 1);
  opacity: 1;
  visibility: visible;
  transition: transform 0.7s ease, opacity 0.7s ease, visibility 0.7s ease;
}

body.megamenu-popup-active {
  overflow: hidden;
}

body.megamenu-popup-active .stricky-header {
  bottom: 0;
}

body.megamenu-popup-active .mobile-nav__content {
  overflow: unset;
}

.mobile-nav__content .demo-one .container {
  padding-left: 15px;
  padding-right: 15px;
}

.megamenu-popup {
  position: relative;
}
.megamenu-popup .megamenu-clickable--close {
  position: absolute;
  top: 18px;
  right: 20px;
  display: block;
  color: var(--wallox-text-dark, #2E2A20);
}
@media (min-width: 1300px) {
  .megamenu-popup .megamenu-clickable--close {
    top: 38px;
    right: 40px;
  }
}
.megamenu-popup .megamenu-clickable--close:hover {
  color: var(--wallox-base, #DF9E42);
}
.megamenu-popup .megamenu-clickable--close span {
  width: 24px;
  height: 24px;
  display: block;
  position: relative;
  color: currentColor;
  transition: all 500ms ease;
}
.megamenu-popup .megamenu-clickable--close span::before, .megamenu-popup .megamenu-clickable--close span::after {
  content: "";
  width: 100%;
  height: 2px;
  background-color: currentColor;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
}
.megamenu-popup .megamenu-clickable--close span::after {
  transform: translate(-50%, -50%) rotate(45deg);
}

/*--------------------------------------------------------------
# Side Bar Head
--------------------------------------------------------------*/
/*Header Right Menu Style*/
.header-right-sidebar {
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 999;
  transform: translateX(100%);
  transform-origin: left center;
  transition: transform 600ms ease 600ms, visibility 600ms ease 600ms;
  position: fixed;
  visibility: hidden;
  background: transparent;
}
.header-right-sidebar__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--wallox-text-dark, #2E2A20);
  opacity: 0.66;
  cursor: url(../images/close.png), auto;
}
.header-right-sidebar__content {
  max-width: 460px;
  z-index: 10;
  position: relative;
  height: 100%;
  margin-left: auto;
  background-color: var(--wallox-text-dark, #2E2A20);
  margin-right: 0;
  overflow-y: auto;
  padding: 50px 44px;
  overflow: hidden;
  overflow-y: auto;
  opacity: 0;
  visibility: hidden;
  transform: translateX(100%);
  transition: opacity 600ms ease 0ms, visibility 600ms ease 0ms, transform 600ms ease 0ms;
}
.header-right-sidebar__close {
  font-size: 22px;
  color: var(--wallox-white, #fff);
  position: absolute;
  padding: 30px;
  top: 0px;
  right: 30px;
  cursor: pointer;
}
.header-right-sidebar__logo-box {
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(var(--wallox-border-color-rgb, 228, 218, 204), 0.5);
}
.header-right-sidebar__container {
  padding-top: 40px;
}
.header-right-sidebar__container__about {
  padding-bottom: 40px;
}
.header-right-sidebar__container__title {
  font-style: normal;
  font-weight: 600;
  font-size: 25px;
  line-height: 36px;
  color: var(--wallox-white, #fff);
  text-transform: capitalize;
  margin-bottom: 22px;
}
.header-right-sidebar__container__text {
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 175%;
  margin-bottom: 0;
  padding-bottom: 0;
  color: var(--wallox-text, #7E7C76);
}
.header-right-sidebar__container__list {
  padding-bottom: 40px;
}
.header-right-sidebar__container__list__item {
  display: flex;
  align-items: center;
  gap: 15px;
}
.header-right-sidebar__container__list__item + .header-right-sidebar__container__list__item {
  margin-top: 25px;
}
.header-right-sidebar__container__list__title {
  display: block;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  margin-bottom: 0;
  padding-bottom: 0;
  text-transform: capitalize;
  color: var(--wallox-text, #7E7C76);
}
.header-right-sidebar__container__list a, .header-right-sidebar__container__list p {
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 21px;
  margin-bottom: 0;
  padding-bottom: 0;
  color: var(--wallox-gray, #F4EDE4);
}
.header-right-sidebar__container__list a {
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.header-right-sidebar__container__list a:hover {
  background-size: 100% 1px;
}
.header-right-sidebar__container__list a:hover {
  color: var(--wallox-base, #DF9E42);
}
.header-right-sidebar__container__icon {
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  border-radius: 50%;
  color: var(--wallox-base, #DF9E42);
  background: var(--wallox-gray, #F4EDE4);
}
.header-right-sidebar__container .newsletter-box input[type=email] {
  width: 100%;
  border: none;
  border-radius: 6px;
  outline: none;
  height: 55px;
  background-color: var(--wallox-white, #fff);
  color: var(--wallox-text, #7E7C76);
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 175%;
  padding-left: 30px;
  padding-right: 60px;
  transition: all 500ms ease;
}
.header-right-sidebar__container .newsletter-box input[type=email]:focus {
  color: var(--wallox-white, #fff);
}
.header-right-sidebar__container .newsletter-box input[type=email]::placeholder {
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 175%;
  color: var(--wallox-text, #7E7C76);
}
.header-right-sidebar__container .newsletter-box button {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 100%;
  text-transform: capitalize;
  background: var(--wallox-base, #DF9E42);
  width: 100%;
  margin-top: 10px;
  border: none;
  border: 1px solid transparent;
  padding: 15px;
  border-radius: 10px;
}
.header-right-sidebar__container .newsletter-box button:hover {
  color: var(--wallox-base, #DF9E42);
  background-color: transparent;
  border-color: var(--wallox-base, #DF9E42);
}
.header-right-sidebar__container .newsletter-box button::before {
  background: transparent;
}
.header-right-sidebar.isActive {
  visibility: visible;
  transform: translateX(0%);
}
.header-right-sidebar.isActive .header-right-sidebar__content {
  visibility: visible;
  opacity: 1;
  transform: translateX(0%);
}

/*--------------------------------------------------------------
# Home Showcase
--------------------------------------------------------------*/
.demo-one {
  padding-top: 120px;
  padding-bottom: 120px;
}
.demo-one .row {
  --bs-gutter-y: 30px;
}
.demo-one__card {
  background-color: var(--wallox-white, #fff);
  box-shadow: 0px 10px 60px 0px rgba(var(--wallox-text-dark-rgb, 46, 42, 32), 0.1);
  text-align: center;
  transition: 500ms ease;
  transform: translateY(0px);
}
.demo-one__card:hover {
  transform: translateY(-10px);
}
.demo-one__title {
  margin: 0;
  text-transform: capitalize;
  font-size: 16px;
  color: var(--wallox-text-dark, #2E2A20);
  font-weight: 600;
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
}
.demo-one__title a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.demo-one__title a:hover {
  background-size: 100% 1px;
}
.demo-one__image {
  position: relative;
  overflow: hidden;
}
.demo-one__image img {
  max-width: 100%;
  transition: filter 500ms ease;
  filter: blur(0px);
}
.demo-one__card:hover .demo-one__image img {
  filter: blur(2px);
}
.demo-one__btns {
  background-color: rgba(var(--wallox-text-dark-rgb, 46, 42, 32), 0.7);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  transform: scale(1, 0);
  transition: transform 500ms ease, opacity 600ms linear;
  transform-origin: bottom center;
  opacity: 0;
}
.demo-one__card:hover .demo-one__btns {
  transform: scale(1, 1);
  opacity: 1;
  transform-origin: top center;
}
.demo-one__btn {
  font-size: 14px;
  padding: 13px 30px;
  border-radius: 10px;
}
.demo-one__btn:hover {
  color: var(--wallox-white, #fff);
}
@media (min-width: 768px) {
  .demo-one__btn {
    display: inline-flex;
  }
}
.demo-one .wallox-btn::before {
  border-radius: 10px;
}
.demo-one__title {
  padding-top: 20.5px;
  padding-bottom: 20.5px;
}

.home-showcase .row {
  --bs-gutter-x: 42px;
  --bs-gutter-y: 20px;
}
.home-showcase__inner {
  padding: 40px 40px 21px;
  background-color: var(--wallox-white, #fff);
  box-shadow: 0px 10px 60px 0px rgba(var(--wallox-text-dark-rgb, 46, 42, 32), 0.07);
}
.home-showcase .demo-one__card {
  box-shadow: none;
}
.home-showcase .demo-one__btns {
  flex-direction: column;
}
.home-showcase .demo-one__btn {
  min-width: 140px;
  text-align: center;
  justify-content: center;
}
.home-showcase .demo-one__btn::before {
  border-radius: 10px;
}
.home-showcase .demo-one__title {
  padding: 0;
  font-size: 18px;
  margin-top: 15px;
  padding-bottom: 15px;
}

/*--------------------------------------------------------------
# Why choose
--------------------------------------------------------------*/
/****why-choose****/
.why-choose {
  position: relative;
  background: var(--wallox-gray, #F4EDE4);
  padding: 120px 0px;
  z-index: 1;
}
@media (max-width: 991px) {
  .why-choose {
    padding: 100px 0px;
  }
}
@media (max-width: 767px) {
  .why-choose {
    padding: 80px 0px;
  }
}
.why-choose__bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-repeat: no-repeat;
  background-size: cover;
  opacity: 0.15;
}
.why-choose .sec-title {
  padding-bottom: 19px;
}
.why-choose__top__text {
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 175%;
  padding-bottom: 0;
  text-transform: capitalize;
  color: var(--wallox-text, #7E7C76);
  margin-bottom: 22px;
}
.why-choose__box {
  border-radius: 20px;
  background: var(--wallox-white, #fff);
  position: relative;
  padding: 30px;
}
.why-choose__box__list {
  display: flex;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--wallox-border-color, #E4DACC);
  margin-bottom: 25px;
  gap: 29px;
}
.why-choose__box__list__item {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 175%;
  margin-bottom: 0;
  padding-bottom: 0;
  color: var(--wallox-text-dark, #2E2A20);
  text-transform: capitalize;
}
.why-choose__box__list__item i {
  color: var(--wallox-base, #DF9E42);
  margin-right: 10px;
}
.why-choose .progress-box__title {
  font-style: normal;
  font-weight: 700;
  font-size: 16px;
  line-height: 175%;
  padding-bottom: 0;
  color: var(--wallox-text-dark, #2E2A20);
  margin-bottom: 15px;
}
.why-choose .progress-box__number {
  background: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
  font-style: normal;
  font-weight: 700;
  font-size: 14px;
  line-height: 160%;
  padding-left: 5px;
  padding-right: 5px;
  bottom: calc(100% + 18px);
}
.why-choose .progress-box__number::after {
  content: "";
  position: absolute;
  bottom: -9px;
  right: 0;
  width: 10px;
  height: 10px;
  clip-path: polygon(50% 100%, 0 0, 100% 0);
  background: var(--wallox-base, #DF9E42);
}
.why-choose__shape {
  position: absolute;
  top: 10%;
  left: 0;
  animation: topToBottom 3s ease-in-out infinite;
  z-index: -1;
}
.why-choose__shape--two {
  top: 5%;
  left: auto;
  right: 0;
  animation: topToBottom 3s ease-in-out infinite;
  animation-delay: 1s;
  z-index: -1;
}
.why-choose__right {
  position: relative;
  z-index: 1;
}
.why-choose__thumb {
  border-radius: 20px;
  margin-left: 55px;
}
.why-choose__thumb--two {
  position: absolute;
  bottom: 0;
  left: 0;
}
@media (max-width: 575px) {
  .why-choose__thumb--two img {
    object-fit: cover;
    width: 100%;
  }
}

/****why-choose-one****/
.why-choose-one {
  position: relative;
  background: var(--wallox-white, #fff);
  padding: 120px 0px;
}
@media (max-width: 991px) {
  .why-choose-one {
    padding: 100px 0px;
  }
}
@media (max-width: 767px) {
  .why-choose-one {
    padding: 80px 0px;
  }
}
.why-choose-one .sec-title {
  padding-bottom: 20px;
}
.why-choose-one__top__text {
  font-style: italic;
  font-weight: 600;
  font-size: 20px;
  line-height: 180%;
  text-transform: capitalize;
  color: var(--wallox-base, #DF9E42);
}
.why-choose-one__feature {
  margin-top: 35px;
  margin-bottom: 37px;
  border-top: 1px solid var(--wallox-border-color, #E4DACC);
  border-bottom: 1px solid var(--wallox-border-color, #E4DACC);
  padding-top: 27px;
  padding-bottom: 27px;
  display: flex;
  align-items: center;
  gap: 40px;
}
@media (max-width: 575px) {
  .why-choose-one__feature {
    flex-direction: column;
    justify-content: start;
    align-items: start;
  }
}
.why-choose-one__feature__item {
  flex: 50%;
}
@media (max-width: 575px) {
  .why-choose-one__feature__item {
    flex: 100%;
  }
}
.why-choose-one__feature__item__inner {
  display: flex;
  align-items: center;
  gap: 10px;
}
.why-choose-one__feature__item:hover .why-choose-one__feature__icon {
  color: var(--wallox-white, #fff);
}
.why-choose-one__feature__item:hover .why-choose-one__feature__icon::after {
  top: 0%;
  height: 100%;
  width: 100%;
  left: 0%;
}
.why-choose-one__feature__icon {
  position: relative;
  max-width: 65px;
  width: 100%;
  height: 65px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--wallox-gray, #F4EDE4);
  font-size: 25px;
  color: var(--wallox-base, #DF9E42);
  z-index: 1;
}
.why-choose-one__feature__icon::after {
  content: "";
  position: absolute;
  top: 0%;
  height: 0;
  width: 0;
  left: 50%;
  background-color: var(--wallox-base, #DF9E42);
  border-radius: 50%;
  transition: all 0.4s ease;
  z-index: -1;
}
.why-choose-one__feature__title {
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 140%;
  text-transform: capitalize;
  margin-bottom: 0;
  padding-bottom: 0;
  color: var(--wallox-text-dark, #2E2A20);
}
.why-choose-one__feature__text {
  flex: 50%;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 175%;
  text-transform: capitalize;
  margin-bottom: 0;
  padding-bottom: 0;
  position: relative;
}
@media (max-width: 575px) {
  .why-choose-one__feature__text {
    flex: 100%;
  }
}
.why-choose-one__feature__text::after {
  content: "";
  position: absolute;
  top: 0%;
  height: 100%;
  width: 1px;
  left: -20px;
  background-color: var(--wallox-border-color, #E4DACC);
}
.why-choose-one .progress-box__title {
  font-style: normal;
  font-weight: 700;
  font-size: 16px;
  line-height: 175%;
  padding-bottom: 0;
  color: var(--wallox-text-dark, #2E2A20);
  margin-bottom: 15px;
}
.why-choose-one .progress-box__number {
  background: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
  font-style: normal;
  font-weight: 700;
  font-size: 14px;
  line-height: 160%;
  padding-left: 5px;
  padding-right: 5px;
  bottom: calc(100% + 18px);
}
.why-choose-one .progress-box__number::after {
  content: "";
  position: absolute;
  bottom: -9px;
  right: 0;
  width: 10px;
  height: 10px;
  clip-path: polygon(50% 100%, 0 0, 100% 0);
  background: var(--wallox-base, #DF9E42);
}
.why-choose-one__progress + .why-choose-one__progress {
  margin-top: 30px;
}
.why-choose-one__right {
  position: relative;
  z-index: 1;
}
@media (max-width: 991px) {
  .why-choose-one__right {
    max-width: 500px;
    width: 100%;
  }
}
.why-choose-one__thumb {
  position: relative;
}
.why-choose-one__thumb img {
  border-radius: 10px;
}
@media (max-width: 767px) {
  .why-choose-one__thumb img {
    object-fit: cover;
    width: 100%;
  }
}
.why-choose-one__thumb-two {
  margin-top: -200px;
  border: 10px solid var(--wallox-gray, #F4EDE4);
  box-shadow: 0px 4px 60px 10px rgba(223, 159, 67, 0.08);
  border-radius: 10px;
  display: inline-block;
  position: absolute;
  right: 0;
}
.why-choose-one__thumb-two img {
  border-radius: 10px;
}
@media (max-width: 1199px) and (min-width: 991px) {
  .why-choose-one__thumb-two {
    right: -30%;
  }
}
.why-choose-one__logo {
  position: relative;
}
@media (max-width: 575px) {
  .why-choose-one__logo {
    display: none;
  }
}
.why-choose-one__logo-icon {
  width: 277px;
  height: 277px;
  border-radius: 50%;
  background: transparent;
  position: absolute;
  top: 30%;
  transform: translateY(-100%);
  right: 18%;
}
@media (max-width: 1199px) and (min-width: 991px) {
  .why-choose-one__logo-icon {
    right: 10%;
  }
}
@media (max-width: 767px) {
  .why-choose-one__logo-icon {
    right: 10%;
  }
}
.why-choose-one__logo-icon__thumb {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 228px;
  height: 228px;
  border-radius: 50%;
  border: 15px solid var(--wallox-white, #fff);
  background-color: var(--wallox-gray, #F4EDE4);
}
.why-choose-one .circle-text__curved-circle {
  position: absolute;
  transform: translateY(-103%);
  right: 47%;
}
@media (max-width: 991px) {
  .why-choose-one .circle-text__curved-circle {
    right: 43%;
  }
}
.why-choose-one .circle-text__curved-circle__item {
  font-size: 16px;
  color: var(--wallox-white, #fff);
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 2px;
  word-spacing: 4px;
  animation: rotated 18s infinite linear;
}
.why-choose-one__thumb-box {
  position: absolute;
  top: 0;
  right: 10%;
  width: 88px;
  height: 163px;
  background: var(--wallox-base, #DF9E42);
  border-radius: 15px;
  transform: matrix(-1, 0, 0, 1, 0, 0);
  z-index: -1;
  top: 10%;
  animation: topToBottom 3s ease-in-out infinite;
}
.why-choose-one__thumb-box-two {
  position: absolute;
  width: 308px;
  height: 255px;
  background: var(--wallox-base, #DF9E42);
  border-radius: 127.5px;
  transform: matrix(-1, 0, 0, 1, 0, 0);
  bottom: -20%;
  left: 10%;
  z-index: -1;
  animation: topToBottom 3s ease-in-out infinite;
  animation-delay: 1s;
}

/*--------------------------------------------------------------
# Funfact
--------------------------------------------------------------*/
.funfact-one {
  background-color: var(--wallox-base, #DF9E42);
  background-image: url(../images/shapes/funfact-bg-1-1.jpg);
  background-size: cover;
  background-position: center center;
  padding-top: 80px;
  padding-bottom: 80px;
}
@media (min-width: 1200px) {
  .funfact-one {
    padding-top: 73.5px;
    padding-bottom: 73.5px;
  }
}
.funfact-one__list {
  margin: 0;
}
@media (min-width: 768px) {
  .funfact-one__list {
    display: flex;
    flex-wrap: wrap;
    row-gap: 20px;
  }
}
@media (min-width: 1200px) {
  .funfact-one__list {
    justify-content: space-between;
    gap: 0;
  }
}
.funfact-one__item {
  display: flex;
  align-items: center;
  margin-top: -6px;
  position: relative;
}
.funfact-one__item:not(:first-of-type)::before {
  content: "";
  width: 1px;
  height: 67px;
  background-color: var(--wallox-white, #fff);
  position: absolute;
  top: 50%;
  left: -57px;
  transform: translateY(-50%);
  opacity: 0.3;
  display: none;
}
@media (min-width: 1200px) {
  .funfact-one__item:not(:first-of-type)::before {
    display: block;
  }
}
@media (min-width: 768px) {
  .funfact-one__item {
    flex: 0 0 50%;
    max-width: 50%;
  }
}
@media (min-width: 1200px) {
  .funfact-one__item {
    flex: 0 0 auto;
    max-width: none;
  }
}
.funfact-one__item:hover .funfact-one__icon {
  transform: rotateY(360deg);
}
.funfact-one__item + .funfact-one__item {
  margin-top: 20px;
}
@media (min-width: 768px) {
  .funfact-one__item + .funfact-one__item {
    margin-top: 0;
  }
}
.funfact-one__icon {
  font-size: 60px;
  color: var(--wallox-white, #fff);
  margin-right: 20px;
  flex-shrink: 0;
  -webkit-transition: all 0.6s ease;
  -o-transition: all 0.6s ease;
  transition: all 0.6s ease;
}
.funfact-one__count {
  margin: 0;
  color: var(--wallox-white, #fff);
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  font-size: 35px;
  font-weight: 500;
  line-height: 1;
}
@media (min-width: 768px) {
  .funfact-one__count {
    font-size: 40px;
  }
}
.funfact-one__text {
  margin: 0;
  color: var(--wallox-white, #fff);
  font-size: 16px;
  line-height: 36px;
  margin-top: 7px;
  margin-bottom: -6px;
}

.funfact-two {
  position: relative;
  background-color: var(--wallox-base, #DF9E42);
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  padding: 120px 0;
}
.funfact-two__shape {
  position: absolute;
  left: -10px;
  top: 0;
}
.funfact-two__shape img {
  animation: shapeMove 3s linear 0s infinite;
}
@media (max-width: 767px) {
  .funfact-two {
    padding: 80px 0;
  }
}
.funfact-two .sec-title {
  padding-bottom: 33px;
}
.funfact-two .sec-title__tagline,
.funfact-two .sec-title__title {
  color: var(--wallox-white, #fff);
}
.funfact-two__list {
  position: relative;
  background-color: var(--wallox-white, #fff);
  margin: 0 0 0 -7px;
  padding: 0;
  list-style: none;
  display: flex;
  flex-wrap: wrap;
  row-gap: 0;
}
@media (max-width: 991px) {
  .funfact-two__list {
    margin: 50px 0 0;
  }
}
.funfact-two__list__icon {
  width: 94px;
  height: 94px;
  background-color: var(--wallox-base, #DF9E42);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  position: absolute;
  right: 0;
  left: 0;
  top: 0;
  bottom: 0;
  margin: auto;
}
.funfact-two__list__icon img {
  height: 65px;
  transform: scale(1);
  transition: 500ms ease;
}
@media (max-width: 767px) {
  .funfact-two__list__icon {
    display: none;
  }
}
.funfact-two__list:hover .funfact-two__list__icon img {
  transform: scale(0.9);
}
.funfact-two__item {
  flex: 0 0 50%;
  max-width: 50%;
  padding: 42px 58px;
}
.funfact-two__item:nth-child(1), .funfact-two__item:nth-child(3) {
  border-right: 1px solid var(--wallox-base, #DF9E42);
}
.funfact-two__item:nth-child(1), .funfact-two__item:nth-child(2) {
  border-bottom: 1px solid var(--wallox-base, #DF9E42);
}
@media (max-width: 1199px) {
  .funfact-two__item {
    padding-left: 30px;
    padding-right: 25px;
  }
}
@media (max-width: 991px) {
  .funfact-two__item {
    padding-left: 50px;
  }
}
@media (max-width: 767px) {
  .funfact-two__item {
    padding-left: 50px;
    flex: 0 0 100%;
    max-width: 100%;
    border-bottom: 1px solid var(--wallox-base, #DF9E42);
  }
}
.funfact-two__count {
  margin: 0;
  color: var(--wallox-text-dark, #2E2A20);
  font-size: 40px;
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  font-weight: 500;
  line-height: 1;
  margin-bottom: 0px;
}
@media (min-width: 768px) {
  .funfact-two__count {
    font-size: 50px;
  }
}
.funfact-two__text {
  margin: 0;
  font-size: 18px;
  margin: 0;
}

.funfact-three {
  position: relative;
  padding: 100px 0;
}
@media (max-width: 767px) {
  .funfact-three {
    padding: 80px 0 50px;
  }
}
.funfact-three__bg {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  background-color: var(--wallox-gray, #F4EDE4);
}
@media (min-width: 1400px) {
  .funfact-three__bg {
    width: calc(100% - 240px);
    left: 120px;
  }
}
.funfact-three__list {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  flex-wrap: wrap;
  row-gap: 0;
}
@media (max-width: 767px) {
  .funfact-three__list {
    display: block;
  }
}
.funfact-three__item {
  flex: 0 0 25%;
  max-width: 25%;
  position: relative;
}
@media (max-width: 991px) {
  .funfact-three__item {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 0 30px !important;
  }
}
@media (max-width: 767px) {
  .funfact-three__item {
    flex: 0 0 100%;
    max-width: 100%;
    padding: 0 0 30px !important;
    text-align: center;
  }
}
.funfact-three__item:not(:last-of-type)::before {
  content: "";
  width: 1px;
  height: 100%;
  background-color: var(--wallox-border-color, #E4DACC);
  position: absolute;
  top: 0;
  right: 54px;
}
@media (max-width: 991px) {
  .funfact-three__item:not(:last-of-type)::before {
    right: 50px !important;
  }
}
@media (max-width: 767px) {
  .funfact-three__item:not(:last-of-type)::before {
    display: none;
  }
}
.funfact-three__item:not(:first-of-type) {
  padding-left: 37px;
}
@media (max-width: 1199px) {
  .funfact-three__item:not(:first-of-type) {
    padding-left: 0;
  }
}
.funfact-three__item:nth-child(2)::before {
  right: 16px;
}
@media (max-width: 991px) {
  .funfact-three__item:nth-child(2)::before {
    display: none;
  }
}
.funfact-three__item:nth-child(3)::before {
  right: -20px;
}
.funfact-three__item:nth-child(3) {
  padding-left: 63px;
}
@media (max-width: 1199px) {
  .funfact-three__item:nth-child(3) {
    padding-left: 40px;
  }
}
.funfact-three__item:last-child {
  padding-left: 104px;
}
@media (max-width: 1199px) {
  .funfact-three__item:last-child {
    padding-left: 70px;
  }
}
.funfact-three__item:hover .funfact-three__icon::before {
  transform: scale(0.9);
}
.funfact-three__icon {
  width: 103px;
  height: 104px;
  background-color: var(--wallox-base, #DF9E42);
  font-size: 60px;
  color: var(--wallox-white, #fff);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 0 13px;
}
@media (max-width: 767px) {
  .funfact-three__icon {
    margin-left: auto;
    margin-right: auto;
  }
}
.funfact-three__icon::before {
  transition: all 500ms linear;
  transition-delay: 0s;
  transition-delay: 0s;
  transition-delay: 0s;
  transition-delay: 0s;
  transition-delay: 0.1s;
  transform: scale(1);
}
.funfact-three__count {
  margin: 0;
  color: var(--wallox-text-dark, #2E2A20);
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  font-size: 35px;
  font-weight: 500;
  line-height: 1;
}
@media (min-width: 768px) {
  .funfact-three__count {
    font-size: 40px;
  }
}
.funfact-three__text {
  margin: 0;
  color: var(--wallox-text, #7E7C76);
  font-size: 16px;
  margin-top: 9px;
  margin-bottom: -6px;
}

/*--------------------------------------------------------------
# Testimonials
--------------------------------------------------------------*/
.testimonials-contact {
  padding: 120px 0px;
  position: relative;
  background: var(--wallox-gray, #F4EDE4);
}
@media (max-width: 991px) {
  .testimonials-contact {
    padding: 100px 0px;
  }
}
@media (max-width: 767px) {
  .testimonials-contact {
    padding: 80px 0px;
  }
}
.testimonials-contact--home {
  background-color: var(--wallox-text-dark, #2E2A20);
  position: relative;
  z-index: 1;
}
.testimonials-contact--home .sec-title__title {
  color: var(--wallox-white, #fff);
}
.testimonials-contact__bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-repeat: no-repeat;
  background-size: cover;
  mix-blend-mode: overlay;
}
.testimonials-contact__right {
  margin-left: 30px;
}
.testimonials-contact__right .sec-title {
  padding-bottom: 40px;
}
.testimonials-contact__carousel .owl-nav {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 40px;
}
.testimonials-contact__carousel .owl-nav button {
  border: none;
  outline: none;
  border-radius: 50% !important;
  margin: 0;
  padding: 0;
  overflow: hidden;
  background: transparent;
}
.testimonials-contact__carousel .owl-nav button span {
  border: 1px solid var(--wallox-base, #DF9E42);
  outline: none;
  overflow: hidden;
  width: 50px;
  height: 50px;
  background-color: var(--wallox-gray, #F4EDE4);
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--wallox-text, #7E7C76);
  border-radius: 50%;
  font-size: 14px;
  color: var(--wallox-text, #7E7C76);
  transition: all 500ms ease;
}
.testimonials-contact__carousel .owl-nav button span:hover {
  background-color: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
}
.testimonials-contact__carousel--two .owl-nav {
  position: relative;
}
.testimonials-contact__carousel--two .owl-nav button {
  background-color: transparent;
}
.testimonials-contact__carousel--two .owl-nav button span {
  background-color: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
  border: 1px solid transparent;
}
.testimonials-contact__carousel--two .owl-nav button span:hover {
  background-color: transparent;
  color: var(--wallox-base, #DF9E42);
  border: 1px solid var(--wallox-base, #DF9E42);
}
.testimonials-contact__carousel--two .owl-nav::after, .testimonials-contact__carousel--two .owl-nav::before {
  content: "";
  width: 35%;
  height: 0.7px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(var(--wallox-border-color-rgb, 228, 218, 204), 0.3);
  left: 0;
}
.testimonials-contact__carousel--two .owl-nav::after {
  right: 0;
  left: auto;
}
.testimonials-contact__item {
  position: relative;
  padding: 40px;
  background-color: var(--wallox-white, #fff);
  border-radius: 20px;
}
.testimonials-contact__item__text {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 175%;
  margin-bottom: -4px;
  padding-bottom: 0;
  color: var(--wallox-text, #7E7C76);
  text-transform: capitalize;
  text-shadow: 0 0 0.1px currentColor;
}
.testimonials-contact__author {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 22px;
  position: relative;
}
.testimonials-contact__author__image {
  max-width: 100px;
  height: 100px;
  width: 100%;
  border-radius: 50%;
  overflow: hidden;
}
.testimonials-contact__author__image img {
  object-fit: cover;
  width: 100%;
  border-radius: 50%;
}
.testimonials-contact__author__name {
  font-style: normal;
  font-weight: 600;
  font-size: 20px;
  line-height: 140%;
  color: var(--wallox-text-dark, #2E2A20);
  margin-bottom: 5px;
  padding-bottom: 0;
  text-transform: capitalize;
  text-shadow: 0 0 0.1px currentColor;
}
.testimonials-contact__author__deg {
  display: block;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 138%;
  margin-bottom: 0px;
  padding-bottom: 0;
  text-transform: capitalize;
  color: var(--wallox-text, #7E7C76);
}
.testimonials-contact__author__star {
  font-size: 15px;
  color: var(--wallox-base, #DF9E42);
}
.testimonials-contact__author__star i:last-child {
  color: var(--wallox-text, #7E7C76);
}
.testimonials-contact__author__quite {
  font-size: 30px;
  color: var(--wallox-base, #DF9E42);
  position: absolute;
  top: 0px;
  right: 0;
  line-height: 0;
}
.testimonials-contact__left {
  position: relative;
  z-index: 1;
  background-color: var(--wallox-base, #DF9E42);
  border-radius: 20px;
  padding: 60px;
  overflow: hidden;
}
@media (max-width: 767px) {
  .testimonials-contact__left {
    padding: 40px;
  }
}
@media (max-width: 575px) {
  .testimonials-contact__left {
    padding: 30px;
  }
}
.testimonials-contact__left .contact-one__form {
  margin-left: 0;
}
.testimonials-contact__left .form-one__control label {
  margin-top: -8px;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 175%;
  display: block;
  margin-bottom: 9px;
  padding-bottom: 0;
  color: var(--wallox-white, #fff);
  text-transform: capitalize;
}
.testimonials-contact__left .form-one__control textarea {
  height: 110px;
}
.testimonials-contact__left .sec-title {
  padding-bottom: 20px;
}
.testimonials-contact__left .sec-title__tagline {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  color: var(--wallox-white, #fff);
}
.testimonials-contact__left .sec-title__title {
  font-style: normal;
  font-weight: 800;
  font-size: 30px;
  line-height: 153%;
  color: var(--wallox-white, #fff);
}
.testimonials-contact__left .wallox-btn__submite {
  border-radius: 6px;
  width: 100%;
  text-align: center;
  display: flex;
  justify-content: center;
  transform: scale(1);
}
.testimonials-contact__left .wallox-btn__submite::before {
  background: var(--wallox-white, #fff);
  border-radius: 6px;
}
.testimonials-contact__left .wallox-btn__submite:hover {
  color: var(--wallox-text-dark, #2E2A20);
  transform: scale(1);
}
.testimonials-contact__bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-repeat: no-repeat;
  background-size: cover;
  mix-blend-mode: multiply;
  opacity: 0.15;
}

.testimonials-card {
  padding: 30px;
  position: relative;
  z-index: 1;
}
@media (max-width: 1199px) and (min-width: 992px) {
  .testimonials-card {
    padding: 20px;
    border: 2px solid var(--wallox-border-color, #E4DACC);
    border-radius: 20px;
  }
}
@media (max-width: 991px) {
  .testimonials-card {
    max-width: 560px;
    margin-left: auto;
    margin-right: auto;
  }
}
@media (max-width: 767px) {
  .testimonials-card {
    padding: 20px;
    border: 2px solid var(--wallox-border-color, #E4DACC);
    border-radius: 20px;
  }
}
.testimonials-card__top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}
.testimonials-card__top__video a {
  width: 74px;
  height: 74px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--wallox-gray, #F4EDE4);
  font-size: 20px;
  line-height: 1;
}
.testimonials-card__top__video a:hover {
  background-color: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
}
.testimonials-card__image {
  width: 74px;
  height: 74px;
  border-radius: 50%;
  overflow: hidden;
}
.testimonials-card__image img {
  object-fit: cover;
  width: 100%;
}
.testimonials-card::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  background-image: url(../images/shapes/testi-border.png);
  background-repeat: no-repeat;
  background-position: top left;
  background-size: contain;
  width: 100%;
  height: 100%;
  z-index: -1;
}
@media (max-width: 1199px) and (min-width: 992px) {
  .testimonials-card::after {
    display: none;
  }
}
@media (max-width: 767px) {
  .testimonials-card::after {
    display: none;
  }
}
.testimonials-card__text {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 175%;
  margin-bottom: 30px;
  padding-bottom: 0;
  color: var(--wallox-text, #7E7C76);
}
.testimonials-card__content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-right: 150px;
}
@media (max-width: 1199px) and (min-width: 992px) {
  .testimonials-card__content {
    margin-right: 0;
  }
}
@media (max-width: 575px) {
  .testimonials-card__content {
    margin-right: 0;
  }
}
.testimonials-card__author__title {
  font-style: normal;
  font-weight: 600;
  font-size: 20px;
  line-height: 140%;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
  padding-bottom: 0;
  margin-bottom: 4px;
}
.testimonials-card__author__dec {
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 138%;
  margin-bottom: 0;
  padding-bottom: 0;
  text-transform: capitalize;
  color: var(--wallox-text, #7E7C76);
  display: block;
}
.testimonials-card__rating {
  color: var(--wallox-base, #DF9E42);
  letter-spacing: 0.1em;
}
.testimonials-card__rating i:last-child {
  color: var(--wallox-text, #7E7C76);
}
.testimonials-card__quite {
  position: absolute;
  bottom: 20px;
  right: 30px;
  line-height: 0;
  font-size: 45px;
  color: var(--wallox-base, #DF9E42);
  transition: all 0.4s ease-in-out;
}
@media (max-width: 1199px) and (min-width: 992px) {
  .testimonials-card__quite {
    display: none;
  }
}
@media (max-width: 575px) {
  .testimonials-card__quite {
    display: none;
  }
}
.testimonials-card:hover .testimonials-card__quite {
  color: var(--wallox-text-dark, #2E2A20);
}

.testimonials-one {
  padding: 120px 0px;
  position: relative;
}
@media (max-width: 991px) {
  .testimonials-one {
    padding: 100px 0px;
  }
}
@media (max-width: 767px) {
  .testimonials-one {
    padding: 80px 0px;
  }
}

/**testimonials-Two**/
.testimonials-two {
  padding: 120px 0px;
  position: relative;
}
@media (max-width: 991px) {
  .testimonials-two {
    padding: 100px 0px;
  }
}
@media (max-width: 767px) {
  .testimonials-two {
    padding: 80px 0px;
  }
}

/**testimonials-three**/
.testimonials-three {
  padding: 120px 0px;
  position: relative;
  background-color: var(--wallox-white, #fff);
}
@media (max-width: 991px) {
  .testimonials-three {
    padding: 100px 0px;
  }
}
@media (max-width: 767px) {
  .testimonials-three {
    padding: 80px 0px;
  }
}
.testimonials-three__inner {
  position: relative;
  border-radius: 30px;
  padding: 60px 40px;
  z-index: 1;
}
.testimonials-three__inner__bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-repeat: no-repeat;
  background-size: cover;
  border-radius: 30px;
  z-index: -1;
  overflow: hidden;
}
.testimonials-three__inner__bg::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, #DF9F43 0%, rgba(223, 158, 66, 0.79) 100%);
  border-radius: 30px;
}
.testimonials-three__carousel__left {
  background-color: var(--wallox-white, #fff);
  border-radius: 50%;
  max-width: 528px;
  width: 100%;
  padding: 70px 40px;
  position: relative;
}
@media (max-width: 575px) {
  .testimonials-three__carousel__left {
    padding: 40px 20px;
  }
}
.testimonials-three__carousel__left::after {
  content: "";
  width: 115px;
  height: 82px;
  background-color: var(--wallox-white, #fff);
  position: absolute;
  bottom: 30%;
  right: -10%;
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
}
@media (max-width: 575px) {
  .testimonials-three__carousel__left::after {
    display: none;
  }
}
.testimonials-three__item__quite {
  text-align: center;
  margin-bottom: 50px;
  display: flex !important;
  align-items: center;
  justify-content: center;
}
.testimonials-three__item__text {
  margin-top: -10px;
  font-style: italic;
  font-weight: 600;
  font-size: 24px;
  line-height: 150%;
  padding-bottom: 0;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
  max-width: 440px;
  width: 100%;
  text-align: center;
  margin-bottom: 45px;
}
@media (max-width: 575px) {
  .testimonials-three__item__text {
    font-size: 16px;
    margin-bottom: 15px;
  }
}
.testimonials-three__item__name {
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 150%;
  text-align: center;
  letter-spacing: -0.02em;
  text-transform: uppercase;
  margin-bottom: 0;
  padding-bottom: 0;
}
@media (max-width: 575px) {
  .testimonials-three__item__name {
    font-size: 14px;
  }
}
.testimonials-three__item__dec {
  font-style: normal;
  font-weight: 400;
  font-size: 15px;
  line-height: 200%;
  text-align: center;
  letter-spacing: -0.02em;
  text-transform: uppercase;
  display: block;
  margin-bottom: 0px;
  padding-bottom: 0;
}
@media (max-width: 575px) {
  .testimonials-three__item__dec {
    font-size: 12px;
  }
}
.testimonials-three__item__star {
  text-align: center;
  color: var(--wallox-base, #DF9E42);
}
.testimonials-three__carousel__right {
  position: relative;
  z-index: 1;
  max-width: 374px;
  width: 100%;
  height: 374px;
  border-radius: 50%;
}
@media (max-width: 991px) {
  .testimonials-three__carousel__right {
    margin-left: auto;
    margin-right: auto;
  }
}
@media (max-width: 575px) {
  .testimonials-three__carousel__right {
    height: 300px;
    width: 300px;
  }
}
.testimonials-three__carousel__right::after {
  position: absolute;
  left: -30px;
  top: -30px;
  right: -30px;
  bottom: -30px;
  margin: auto;
  width: calc(100% + 30px);
  height: calc(100% + 30px);
  content: "";
  border-style: dashed;
  border-width: 2px;
  border-color: var(--wallox-white, #fff);
  border-radius: 50%;
  animation-duration: 1500ms;
  animation: rotated 20s infinite linear;
  transition: 500ms all ease;
  animation-play-state: running;
}
.testimonials-three__thumb__carousel {
  position: relative;
}
.testimonials-three__thumb__carousel .slick-button--prev,
.testimonials-three__thumb__carousel .slick-button--next {
  border: none;
  outline: none;
  border-radius: 50%;
  margin: 0;
  padding: 0;
  position: absolute;
  z-index: 2;
  top: 50%;
  transform: translateY(-50%);
  left: 20px;
  background-color: transparent;
}
.testimonials-three__thumb__carousel .slick-button--prev span,
.testimonials-three__thumb__carousel .slick-button--next span {
  border: none;
  outline: none;
  width: 50px;
  height: 50px;
  background-color: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--wallox-base, #DF9E42);
  border-radius: 50%;
  font-size: 14px;
  transition: all 500ms ease;
  border: 1px solid var(--wallox-white, #fff);
}
.testimonials-three__thumb__carousel .slick-button--prev span:hover,
.testimonials-three__thumb__carousel .slick-button--next span:hover {
  border-color: var(--wallox-base, #DF9E42);
  background-color: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
}
.testimonials-three__thumb__carousel .slick-button--next {
  right: 20px;
  left: auto;
}
@media (max-width: 575px) {
  .testimonials-three__thumb__item {
    width: 300px;
    height: 300px;
    border-radius: 50%;
  }
  .testimonials-three__thumb__item img {
    object-fit: cover;
    width: 100%;
  }
}
.testimonials-three__thumb__item img {
  border-radius: 50%;
}
.testimonials-three__left {
  position: absolute;
  top: 6%;
  left: 50%;
  animation: topToBottom 3s ease-in-out infinite;
}
@media (max-width: 991px) {
  .testimonials-three__left {
    display: none;
  }
}
.testimonials-three__right {
  position: absolute;
  bottom: 5%;
  right: 4%;
  animation: topToBottom 3s ease-in-out infinite;
  animation-delay: 1s;
}
@media (max-width: 991px) {
  .testimonials-three__right {
    display: none;
  }
}
.testimonials-three__element {
  position: absolute;
  top: 20%;
  left: 0;
  animation: topToBottom 4s ease-in-out infinite;
}
.testimonials-three__element-two {
  position: absolute;
  bottom: 20%;
  right: 0;
  animation: rotated 10s ease-in-out infinite;
}

/*--------------------------------------------------------------
# CTA
--------------------------------------------------------------*/
.cta-one {
  padding-bottom: 120px;
  position: relative;
}
.cta-one .container-fluid {
  max-width: 1380px;
}
.cta-one__inner {
  position: relative;
  padding: 20px;
}
.cta-one__bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.cta-one__title {
  margin: 0;
  text-transform: uppercase;
  max-width: 513px;
  width: 100%;
  font-size: 35px;
  line-height: 1.2em;
  font-weight: bold;
  margin-bottom: 15px;
}
.cta-one__title span {
  font-family: var(--wallox-special-font, "Outfit", sans-serif);
  font-weight: 400;
}
@media (min-width: 992px) {
  .cta-one__title {
    font-size: 50px;
    margin-bottom: 30px;
    margin-top: -10px;
  }
}
.cta-one__content {
  position: relative;
  border: 1px solid var(--wallox-white, #fff);
  padding: 50px 20px;
}
@media (min-width: 768px) {
  .cta-one__content {
    padding: 60px;
  }
}
@media (min-width: 1200px) {
  .cta-one__content {
    padding: 100px;
  }
}
.cta-one__link:hover {
  color: var(--wallox-white, #fff);
}
.cta-one__link::after {
  background-color: var(--wallox-text-dark, #2E2A20);
}
.cta-one__link::before {
  background-color: var(--wallox-base, #DF9E42);
}

.cta-two {
  position: relative;
  background-color: var(--wallox-base, #DF9E42);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
}
.cta-two__shape {
  position: absolute;
  left: -10px;
  top: 0;
}
.cta-two__shape img {
  animation: shapeMove 3s linear 0s infinite;
}
.cta-two__content {
  position: relative;
  padding: 85px 0 90px;
}
.cta-two__sub-title {
  font-family: var(--wallox-special-font, "Outfit", sans-serif);
  color: var(--wallox-white, #fff);
  font-size: 40px;
  line-height: 1.2em;
  margin: 0 0 2px;
}
.cta-two__title {
  color: var(--wallox-white, #fff);
  font-size: 50px;
  text-transform: uppercase;
  font-weight: 700;
  margin: 0 0 37px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .cta-two__title {
    font-size: 44px;
  }
}
@media (max-width: 767px) {
  .cta-two__title {
    font-size: 35px;
  }
}
.cta-two__thumb {
  position: relative;
}
.cta-two__thumb__one {
  position: relative;
  right: 35px;
  margin-top: -28px;
}
@media (max-width: 991px) {
  .cta-two__thumb__one {
    right: 0;
  }
}
.cta-two__thumb__one__shape {
  position: absolute;
  left: -40px;
  top: -40px;
  width: 545px;
  height: 408px;
}
.cta-two__thumb__one__thumb {
  width: 482px;
  height: auto;
  border: 20px solid var(--wallox-white, #fff);
  position: relative;
  z-index: 2;
  transform: rotate(5deg);
}
@media (max-width: 767px) {
  .cta-two__thumb__one__thumb {
    width: 100%;
  }
}
.cta-two__thumb__one__thumb img {
  width: 100%;
  height: 282px;
  object-fit: cover;
}
@media (max-width: 767px) {
  .cta-two__thumb__one__thumb img {
    height: auto;
  }
}
.cta-two__thumb__two {
  position: absolute;
  right: -48px;
  top: 195px;
  z-index: 3;
}
@media (max-width: 991px) {
  .cta-two__thumb__two {
    top: 45px;
    right: 0;
  }
}
@media (max-width: 767px) {
  .cta-two__thumb__two {
    position: relative;
    top: 0;
    right: 0;
  }
}
.cta-two__thumb__two__flower {
  position: absolute;
  right: -10px;
  top: -121px;
  z-index: 4;
}
@media (max-width: 767px) {
  .cta-two__thumb__two__flower {
    display: none;
  }
}
.cta-two__thumb__two__flower img {
  -webkit-animation-name: float-bob-y-2;
  animation-name: float-bob-y-2;
  -webkit-animation-duration: 4s;
  animation-duration: 4s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
}
.cta-two__thumb__two__shape {
  position: absolute;
  left: -45px;
  top: -60px;
  width: 446px;
  height: 421px;
}
.cta-two__thumb__two__thumb {
  width: 360px;
  height: auto;
  border: 20px solid var(--wallox-white, #fff);
  position: relative;
  z-index: 2;
  transform: rotate(-17.2deg);
}
@media (max-width: 767px) {
  .cta-two__thumb__two__thumb {
    width: 100%;
  }
}
.cta-two__thumb__two__thumb img {
  width: 100%;
  height: 215px;
  object-fit: cover;
}
@media (max-width: 767px) {
  .cta-two__thumb__two__thumb img {
    height: auto;
  }
}

/*--------------------------------------------------------------
# Gallery
--------------------------------------------------------------*/
.gallery-one {
  padding-top: 120px;
  padding-bottom: 120px;
}
.gallery-one .container-fluid {
  width: 100%;
  max-width: 1572px;
}
.gallery-one--page {
  padding-top: 100px;
}
.gallery-one .row {
  --bs-gutter-x: 30px;
  --bs-gutter-y: 30px;
}
@media (min-width: 992px) {
  .gallery-one__carousel .owl-nav {
    display: none;
  }
}
.gallery-one__filter__list {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40px;
  flex-wrap: wrap;
  gap: 10px;
}
.gallery-one__filter__list li {
  cursor: pointer;
}
.gallery-one__filter__list li span {
  display: block;
  font-size: 14px;
  background-color: var(--wallox-gray, #F4EDE4);
  transition: all 500ms ease;
  text-transform: capitalize;
  font-weight: 600;
  padding: 12px 20px;
  line-height: 1.2em;
  letter-spacing: 0;
}
.gallery-one__filter__list li.active span, .gallery-one__filter__list li:hover span {
  background-color: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
}
.gallery-one__card {
  position: relative;
  overflow: hidden;
  background-color: var(--wallox-text-dark, #2E2A20);
  border-radius: 20px;
}
.gallery-one__card img {
  transform: scale(1);
  max-width: 100%;
  transition: transform 500ms ease, opacity 500ms ease;
  opacity: 1;
}
.gallery-one__card__hover {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(var(--wallox-text-dark-rgb, 46, 42, 32), 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  transform: scale(1, 0);
  transition: transform 500ms ease;
  transform-origin: bottom center;
}
.gallery-one__card__hover .img-popup {
  position: relative;
}
.gallery-one__card:hover img {
  transform: scale(1.05);
  opacity: 0.9;
  mix-blend-mode: screen;
}
.gallery-one__card:hover .gallery-one__card__hover {
  transform-origin: top center;
  transform: scale(1, 1);
}
.gallery-one__card__icon {
  width: 33px;
  height: 33px;
  display: block;
  position: relative;
}
.gallery-one__card__icon::after, .gallery-one__card__icon::before {
  content: "";
  width: 2px;
  height: 100%;
  background-color: var(--wallox-white, #fff);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.4s ease-in-out;
}
.gallery-one__card__icon::after {
  transform: translate(-50%, -50%) rotate(90deg);
}
.gallery-one__card__icon:hover::after, .gallery-one__card__icon:hover::before {
  background-color: var(--wallox-base, #DF9E42);
}

.gallery-two {
  position: relative;
  overflow: hidden;
  padding: 0 0 20px;
}
.gallery-two .container-fluid {
  width: 100%;
  padding-left: 0;
  padding-right: 0;
}
@media (max-width: 767px) {
  .gallery-two .container-fluid {
    padding-left: 15px;
    padding-right: 15px;
  }
}
.gallery-two .row {
  --bs-gutter-x: 20px;
  --bs-gutter-y: 20px;
}
.gallery-two__col-one {
  width: 65%;
}
@media (max-width: 1199px) {
  .gallery-two__col-one {
    width: 100%;
  }
}
.gallery-two__col-two {
  width: 35%;
}
@media (max-width: 1199px) {
  .gallery-two__col-two {
    width: 100%;
  }
}
.gallery-two__card {
  position: relative;
  overflow: hidden;
  background-color: var(--wallox-text-dark, #2E2A20);
}
.gallery-two__card img {
  transform: scale(1);
  width: 100%;
  transition: transform 500ms ease, opacity 500ms ease;
  opacity: 1;
}
.gallery-two__card__hover {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(var(--wallox-text-dark-rgb, 46, 42, 32), 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  transform: scale(1, 0);
  transition: transform 500ms ease;
  transform-origin: bottom center;
}
.gallery-two__card__hover .img-popup {
  position: relative;
}
.gallery-two__card:hover img {
  transform: scale(1.05);
  opacity: 0.9;
  mix-blend-mode: screen;
}
.gallery-two__card:hover .gallery-two__card__hover {
  transform-origin: top center;
  transform: scale(1, 1);
}
.gallery-two__card a {
  width: 75px;
  height: 75px;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  background-color: var(--wallox-white, #fff);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.gallery-two__card a:hover .gallery-two__card__icon::after, .gallery-two__card a:hover .gallery-two__card__icon::before {
  background-color: var(--wallox-base, #DF9E42);
}
.gallery-two__card__icon {
  width: 24px;
  height: 24px;
  display: block;
  position: relative;
}
.gallery-two__card__icon::after, .gallery-two__card__icon::before {
  content: "";
  width: 2px;
  height: 100%;
  background-color: var(--wallox-text-dark, #2E2A20);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: all 400ms ease;
}
.gallery-two__card__icon::after {
  transform: translate(-50%, -50%) rotate(90deg);
}
.gallery-two__info {
  position: relative;
  background-color: var(--wallox-base, #DF9E42);
  background-position: left bottom;
  background-repeat: no-repeat;
  background-size: cover;
  padding: 0 50px 0 100px;
  height: 100%;
}
@media (min-width: 1200px) {
  .gallery-two__info {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    flex-direction: column;
  }
}
@media (max-width: 1500px) {
  .gallery-two__info {
    padding-left: 40px;
    padding-right: 30px;
  }
}
@media (max-width: 1199px) {
  .gallery-two__info {
    padding: 80px 50px;
  }
}
@media (max-width: 767px) {
  .gallery-two__info {
    padding: 50px 30px;
  }
}
.gallery-two__info__icon {
  width: 60px;
  height: 60px;
  margin-bottom: 24px;
}
.gallery-two__info__icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.gallery-two__info__title {
  color: var(--wallox-white, #fff);
  font-size: 44px;
  line-height: 50px;
  text-transform: uppercase;
  font-weight: 700;
  margin: 0;
}
@media (min-width: 1200px) and (max-width: 1300px) {
  .gallery-two__info__title {
    font-size: 35px;
    line-height: 42px;
  }
}
@media (max-width: 767px) {
  .gallery-two__info__title {
    font-size: 35px;
    line-height: 45px;
  }
}

/*--------------------------------------------------------------
# Sidebar
--------------------------------------------------------------*/
/*blog sidebar*/
.sidebar__single {
  background-color: var(--wallox-gray, #F4EDE4);
  padding: 30px;
  border-radius: 10px;
}
.sidebar__single + .sidebar__single {
  margin-top: 30px;
}
.sidebar__title {
  text-transform: capitalize;
  font-style: normal;
  font-weight: 600;
  font-size: 20px;
  line-height: 80%;
  padding-bottom: 0;
  color: var(--wallox-text-dark, #2E2A20);
  margin-bottom: 28px;
  text-shadow: 0 0 0.1px currentColor;
}
.sidebar__form__title {
  margin-top: -6px;
}
.sidebar__search {
  position: relative;
}
.sidebar__search input[type=search],
.sidebar__search input[type=text] {
  outline: none;
  width: 100%;
  height: 60px;
  background-color: var(--wallox-white, #fff);
  color: var(--wallox-text, #7E7C76);
  border: none;
  padding-left: 20px;
  padding-right: 20px;
  transition: all 500ms ease;
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  color: var(--wallox-text, #7E7C76);
  border-radius: 6px;
}
.sidebar__search input[type=search]:focus,
.sidebar__search input[type=text]:focus {
  box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.1);
}
.sidebar__search input[type=search]::placeholder,
.sidebar__search input[type=text]::placeholder {
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  color: var(--wallox-text, #7E7C76);
  text-transform: capitalize;
}
.sidebar__search button[type=submit] {
  border: none;
  outline: none;
  background-color: rgba(0, 0, 0, 0);
  position: absolute;
  top: 50%;
  right: 20px;
  line-height: 1;
  transform: translateY(-50%);
  width: auto;
  font-size: 20px;
  color: var(--wallox-base, #DF9E42);
}
.sidebar__posts {
  margin-bottom: 0;
}
.sidebar__posts__item {
  display: flex;
  align-items: center;
}
.sidebar__posts__item:hover .sidebar__posts__image::after {
  width: 100%;
  right: auto;
  left: 0;
}
.sidebar__posts__item:hover .sidebar__posts__image img {
  transform: scale(1.2) rotate(-15deg);
}
.sidebar__posts__item + .sidebar__posts__item {
  margin-top: 30px;
}
.sidebar__posts__image {
  flex-shrink: 0;
  margin-right: 15px;
  position: relative;
  width: 80px;
  height: 84px;
  overflow: hidden;
  border-radius: 6px;
}
.sidebar__posts__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.4s ease-in-out;
  border-radius: 6px;
}
.sidebar__posts__image::after {
  content: "";
  width: 0%;
  height: 100%;
  top: 0%;
  left: auto;
  right: 0;
  background-color: rgba(var(--wallox-text-dark-rgb, 46, 42, 32), 0.5);
  position: absolute;
  transition: all 0.4s ease-in-out;
}
.sidebar__posts__title {
  margin: 0;
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 156%;
  margin-bottom: -5px;
  padding-bottom: 0;
  color: var(--wallox-text-dark, #2E2A20);
  text-transform: capitalize;
}
@media (max-width: 1199px) and (min-width: 992px) {
  .sidebar__posts__title {
    font-size: 13px;
  }
}
@media (max-width: 400px) {
  .sidebar__posts__title {
    font-size: 14px;
  }
}
.sidebar__posts__title a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.sidebar__posts__title a:hover {
  background-size: 100% 1px;
}
.sidebar__posts__title a:hover {
  color: var(--wallox-base, #DF9E42);
}
.sidebar__posts__meta {
  margin: 0;
  line-height: 1em;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 15px;
}
.sidebar__posts__meta a {
  display: inline-flex;
  align-items: center;
  color: var(--wallox-text, #7E7C76);
  transition: all 500ms ease;
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  line-height: 133%;
}
.sidebar__posts__meta a:hover {
  color: var(--wallox-base, #DF9E42);
  text-shadow: 0 0 1px currentColor;
}
.sidebar__posts__meta a i {
  color: var(--wallox-base, #DF9E42);
  margin-right: 10px;
}
.sidebar__categories {
  margin-bottom: -14px;
  margin-top: -16px;
}
.sidebar__categories li a {
  color: var(--wallox-text, #7E7C76);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 500ms ease;
  padding: 7px 0;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 100%;
  padding-top: 15px;
  padding-bottom: 15px;
}
.sidebar__categories li a:hover {
  background-color: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
  text-shadow: 0 0 1px currentColor;
  padding: 15px 20px;
}
.sidebar__categories li a:hover span {
  color: var(--wallox-white, #fff);
}
.sidebar__categories li a span {
  color: var(--wallox-text-dark, #2E2A20);
}
.sidebar__tags {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}
.sidebar__tags a {
  background-color: var(--wallox-white, #fff);
  color: var(--wallox-text-dark, #2E2A20);
  transition: all 500ms ease;
  display: inline-flex;
  padding: 8px 10px;
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 114%;
  text-transform: capitalize;
  border-radius: 6px;
}
.sidebar__tags a:hover {
  color: var(--wallox-white, #fff);
  background-color: var(--wallox-base, #DF9E42);
}
.sidebar__comments {
  margin-bottom: 0;
}
.sidebar__comments__item {
  display: flex;
  align-items: center;
}
.sidebar__comments__item + .sidebar__comments__item {
  margin-top: 30px;
}
.sidebar__comments__icon {
  flex-shrink: 0;
  width: 44px;
  height: 44px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--wallox-base, #DF9E42);
  font-size: 22px;
  color: var(--wallox-white, #fff);
  margin-right: 20px;
  border-radius: 50%;
  transition: all 500ms ease;
}
.sidebar__comments__item:hover .sidebar__comments__icon {
  background-color: var(--wallox-text-dark, #2E2A20);
  color: var(--wallox-white, #fff);
}
.sidebar__comments__title {
  margin: 0;
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  color: var(--wallox-text, #7E7C76);
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 156%;
  text-transform: capitalize;
}
.sidebar__comments__title a {
  color: inherit;
  transition: all 500ms ease;
}
.sidebar__comments__title a:hover {
  color: var(--wallox-base, #DF9E42);
}

.service-sidebar__inner {
  border-radius: 20px;
  padding: 30px;
  background: var(--wallox-gray, #F4EDE4);
}
@media (max-width: 1199px) and (min-width: 992px) {
  .service-sidebar__inner {
    padding: 30px 20px;
  }
  .service-sidebar__inner .service-sidebar__contact__number {
    font-size: 20px;
  }
}
@media (max-width: 575px) {
  .service-sidebar__inner {
    padding: 30px 20px;
  }
  .service-sidebar__inner .service-sidebar__contact__number {
    font-size: 20px;
  }
}
.service-sidebar__inner + .service-sidebar__single {
  margin-top: 30px;
}
.service-sidebar__single + .service-sidebar__single {
  margin-top: 40px;
}
.service-sidebar__nav {
  border-top: 0;
  margin-bottom: 0;
  padding-bottom: 10px;
}
.service-sidebar__nav li a {
  display: block;
  padding: 16px 40px;
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 150%;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
  background: var(--wallox-white, #fff);
  position: relative;
  z-index: 1;
  overflow: hidden;
  transition: all 0.6s ease-in-out;
  letter-spacing: -0.9px;
  border-radius: 6px;
}
.service-sidebar__nav li a::after {
  content: "";
  width: 4px;
  position: absolute;
  top: 24px;
  left: -0.0001px;
  bottom: 24px;
  background: var(--wallox-text-dark, #2E2A20);
  transition: all 0.2s ease;
}
.service-sidebar__nav li a::before {
  content: "";
  width: 10px;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background: var(--wallox-base, #DF9E42);
  z-index: -1;
  transform: translateX(-100%);
  transition-duration: 0.4s;
  transition-property: transform;
  transition-timing-function: ease-out;
}
@media (max-width: 1199px) and (min-width: 992px) {
  .service-sidebar__nav li a {
    padding: 16px 20px;
    font-size: 18px;
  }
}
@media (max-width: 575px) {
  .service-sidebar__nav li a {
    padding: 16px 20px;
    font-size: 18px;
  }
}
.service-sidebar__nav li.current a, .service-sidebar__nav li:hover a {
  color: var(--wallox-base, #DF9E42);
}
.service-sidebar__nav li.current a::after, .service-sidebar__nav li:hover a::after {
  width: 4px;
  top: 0px;
  left: -0.0001px;
  bottom: 0px;
  background: var(--wallox-base, #DF9E42);
}
.service-sidebar__nav li.current a::before, .service-sidebar__nav li:hover a::before {
  transform: translateX(0%);
}
.service-sidebar__nav li + li {
  margin-top: 20px;
}
.service-sidebar__contact {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  padding-top: 156px;
}
.service-sidebar__contact__content {
  position: relative;
  z-index: 1;
  padding: 0px 20px 30px;
}
.service-sidebar__contact__content::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url(../images/shapes/sidebar-shape-1-1.png);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  background-color: var(--wallox-base, #DF9E42);
  z-index: -1;
  clip-path: polygon(0 35%, 100% 0, 100% 100%, 0% 100%);
}
.service-sidebar__contact__content:hover .service-sidebar__contact__icon {
  background: var(--wallox-text-dark, #2E2A20);
  color: var(--wallox-base, #DF9E42);
}
.service-sidebar__contact__icon {
  width: 72px;
  height: 72px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--wallox-white, #fff);
  color: var(--wallox-base, #DF9E42);
  font-size: 36px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 25px;
  transition: all 0.4s ease;
}
.service-sidebar__contact__time {
  font-style: normal;
  font-weight: 700;
  font-size: 16px;
  line-height: 120%;
  text-transform: uppercase;
  color: var(--wallox-white, #fff);
  text-align: center;
  margin-bottom: 10px;
}
.service-sidebar__contact__number {
  font-style: normal;
  font-weight: 700;
  font-size: 30px;
  line-height: 120%;
  color: var(--wallox-white, #fff);
  text-transform: uppercase;
  margin-bottom: 0;
  text-align: center;
  padding-bottom: 0;
  letter-spacing: -1.2px;
}
.service-sidebar__contact__number a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.service-sidebar__contact__number a:hover {
  background-size: 100% 1px;
}
.service-sidebar__company {
  display: flex;
  align-items: center;
  gap: 20px;
  background-color: var(--wallox-gray, #F4EDE4);
  padding-right: 30px;
  border-radius: 10px;
  overflow: hidden;
}
.service-sidebar__company__btn {
  position: relative;
  overflow: hidden;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 59px;
  height: 59px;
  background-color: var(--wallox-base, #DF9E42);
  font-size: 20px;
  color: var(--wallox-white, #fff);
  transition: all 400ms ease;
}
.service-sidebar__company__btn:hover {
  background: var(--wallox-text-dark, #2E2A20);
}
.service-sidebar__company__title {
  font-style: normal;
  font-weight: 600;
  font-size: 20px;
  line-height: 26px;
  margin-bottom: 0;
  padding-bottom: 0;
  color: var(--wallox-text-dark, #2E2A20);
  text-transform: capitalize;
}
.service-sidebar__company + .service-sidebar__company {
  margin-top: 20px;
}

/*--------------------------------------------------------------
# Blog details
--------------------------------------------------------------*/
.blog-details .blog-card-two {
  padding-bottom: 0;
  border-bottom: none;
}
.blog-details .blog-card-two__image__item {
  border-radius: 30px;
}
.blog-details .blog-card-two__content {
  padding: 8px 0px 40px;
  position: relative;
  z-index: 2;
}
.blog-details .blog-card-two__title {
  margin-top: -4px;
  margin-bottom: 0;
  padding-bottom: 0;
  color: var(--wallox-text-dark, #2E2A20);
  text-transform: capitalize;
  margin-bottom: 10px;
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 185%;
}
.blog-details .blog-card-two__date {
  left: 30px;
  top: 30px;
}
.blog-details .blog-card-two__text {
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 188%;
  padding-bottom: 0;
  margin-bottom: 0;
  text-transform: capitalize;
  color: var(--wallox-text, #7E7C76);
}
.blog-details .blog-card-two__text + .blog-card-two__text {
  margin-top: 20px;
}
.blog-details__feature__thumb {
  margin-top: 30px;
  margin-bottom: 30px;
}
.blog-details__feature__thumb img {
  border-radius: 20px;
  object-fit: cover;
  width: 100%;
}
.blog-details__list {
  margin-top: 30px;
  margin-left: 0;
  margin-bottom: 0;
  padding-left: 1rem;
}
.blog-details__list__item {
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 100%;
  text-transform: capitalize;
  color: var(--wallox-text, #7E7C76);
}
.blog-details__list__item + .blog-details__list__item {
  margin-top: 15px;
}
.blog-details__meta {
  padding: 20px 0px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 30px;
  border-top: 1px solid var(--wallox-border-color, #E4DACC);
  border-bottom: 1px solid var(--wallox-border-color, #E4DACC);
}
.blog-details__tags, .blog-details__cetagoris {
  display: flex;
  align-items: center;
  gap: 30px;
}
.blog-details__tags__title, .blog-details__cetagoris__title {
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 89%;
  text-transform: uppercase;
  color: var(--wallox-text-dark, #2E2A20);
  margin-bottom: 0;
  padding-bottom: 0;
}
.blog-details__tags .sidebar__cetagoris, .blog-details__cetagoris .sidebar__cetagoris {
  display: flex;
  align-items: center;
  gap: 10px;
}
.blog-details__tags .sidebar__cetagoris a, .blog-details__cetagoris .sidebar__cetagoris a {
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 114%;
  padding: 12px;
  color: var(--wallox-text, #7E7C76);
  background: var(--wallox-gray, #F4EDE4);
  text-transform: capitalize;
  border-radius: 6px;
}
.blog-details__tags .sidebar__cetagoris a:hover, .blog-details__cetagoris .sidebar__cetagoris a:hover {
  color: var(--wallox-white, #fff);
  background: var(--wallox-base, #DF9E42);
}
.blog-details__tags .sidebar__tags, .blog-details__cetagoris .sidebar__tags {
  gap: 0;
}
.blog-details__tags .sidebar__tags a, .blog-details__cetagoris .sidebar__tags a {
  padding: 2px;
  color: var(--wallox-text, #7E7C76);
}
.blog-details__tags .sidebar__tags a:hover, .blog-details__cetagoris .sidebar__tags a:hover {
  color: var(--wallox-base, #DF9E42);
  background: transparent;
}

/*--------------------------------------------------------------
# Comments
--------------------------------------------------------------*/
.comments-one {
  margin-top: 40px;
}
@media (min-width: 1200px) {
  .comments-one {
    margin-top: 55px;
  }
}
.comments-one__title {
  margin: 0;
  text-transform: capitalize;
  font-size: 25px;
  margin-top: -4px;
  margin-bottom: -4px;
  font-style: normal;
  font-weight: 600;
  line-height: 100%;
  color: var(--wallox-text-dark, #2E2A20);
}
@media (min-width: 992px) {
  .comments-one__title {
    font-size: 30px;
  }
}
.comments-one__list {
  margin: 0;
  margin-top: 40px;
}
.comments-one__card {
  margin-bottom: 30px;
  padding-bottom: 30px;
  border-bottom: 1px solid var(--wallox-border-color, #E4DACC);
}
@media (min-width: 768px) {
  .comments-one__card {
    display: flex;
    align-items: flex-start;
  }
}
.comments-one__card__top {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.comments-one__card__image {
  margin-right: 20px;
}
.comments-one__card__image img {
  border-radius: 50%;
}
.comments-one__card__title {
  margin: 0;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
  margin-top: 20px;
  font-style: normal;
  font-weight: 600;
  font-size: 20px;
  line-height: 110%;
  margin-bottom: 15px;
}
.comments-one__card__date {
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 100%;
  margin-bottom: 20px;
  padding-bottom: 0;
  color: var(--wallox-text, #7E7C76);
}
.comments-one__card__text {
  margin-bottom: 20px;
  font-weight: 500;
  font-size: 16px;
  line-height: 188%;
  color: var(--wallox-text, #7E7C76);
  margin-bottom: -13px;
  padding-bottom: 0;
  text-transform: capitalize;
}
.comments-one__card__reply {
  padding: 10.5px 12px;
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 114%;
  border-radius: 6px;
  color: var(--wallox-white, #fff);
}
.comments-one__card__reply::before {
  border-radius: 6px;
}
.comments-one__card__content {
  position: relative;
}

.comments-form {
  margin-top: 40px;
}
@media (min-width: 1200px) {
  .comments-form {
    margin-top: 55px;
  }
}
.comments-form__title {
  margin: 0;
  font-size: 25px;
  font-weight: 700;
  line-height: 123%;
  margin-top: -4px;
  margin-bottom: -4px;
  color: var(--wallox-text-dark, #2E2A20);
}
@media (min-width: 992px) {
  .comments-form__title {
    font-size: 30px;
  }
}
.comments-form input[type=text],
.comments-form input[type=email] {
  background-color: transparent;
  color: var(--wallox-text, #7E7C76);
  font-size: 16px;
  font-weight: 500;
  border-top: none;
  border-left: none;
  border-right: none;
  border-bottom: 1px solid var(--wallox-border-color, #E4DACC);
  outline: none;
  padding-left: 0px;
  padding-right: 30px;
}
.comments-form input[type=text]::placeholder,
.comments-form input[type=email]::placeholder {
  color: var(--wallox-text, #7E7C76);
  font-size: 16px;
  font-weight: 500;
  text-transform: capitalize;
}
.comments-form textarea {
  margin-top: 10px;
  border-radius: 6px;
}
.comments-form textarea::placeholder {
  color: var(--wallox-text, #7E7C76);
  font-size: 16px;
  font-weight: 500;
  text-transform: capitalize;
}
.comments-form__form {
  margin-top: 23px;
}
.comments-form .wallox-btn {
  font-style: normal;
  font-weight: 700;
  font-size: 16px;
  line-height: 21px;
  text-transform: uppercase;
  padding: 11.5px 23.5px;
  border-radius: 6px;
  color: var(--wallox-white, #fff);
}
.comments-form .wallox-btn::before {
  border-radius: 6px;
}

/*--------------------------------------------------------------
# Shop
--------------------------------------------------------------*/
.product {
  position: relative;
}
.product__sidebar {
  position: relative;
  margin-top: 85px;
}
@media (max-width: 991px) {
  .product__sidebar {
    margin-top: 0;
  }
}
.product__sidebar--title {
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 89%;
  padding-bottom: 0;
  margin-top: -5px;
  text-transform: capitalize;
  margin-bottom: 25px;
}
.product__search-box {
  background: var(--wallox-gray, #F4EDE4);
  margin-bottom: 30px;
  padding: 30px;
  border-radius: 10px;
}
.product__search {
  position: relative;
  border-radius: 0;
  position: relative;
}
.product__search input[type=text] {
  width: 100%;
  height: 59px;
  background-color: var(--wallox-white, #fff);
  padding-left: 15px;
  padding-right: 50px;
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  color: var(--wallox-text, #7E7C76);
  border: 1px solid var(--wallox-border-color, #E4DACC);
  outline: none;
  font-weight: 500;
  font-size: 14px;
  border-radius: 6px;
  line-height: 114%;
  outline: none;
  position: relative;
}
.product__search input[type=text]::placeholder {
  font-weight: 500;
  font-size: 14px;
  line-height: 114%;
  color: var(--wallox-text, #7E7C76);
  text-transform: capitalize;
}
.product__search button[type=submit] {
  border: none;
  outline: none;
  background-color: rgba(0, 0, 0, 0);
  position: absolute;
  top: 0%;
  right: 10px;
  transform: translateY(80%);
  width: auto;
  font-size: 20px;
  line-height: 0;
  color: var(--wallox-base, #DF9E42);
}
.product__price-ranger {
  background-color: var(--wallox-gray, #F4EDE4);
  padding: 30px;
  margin-bottom: 30px;
  border-radius: 10px;
}
.product__price-ranger .price-ranger {
  background: var(--wallox-white, #fff);
  border: 1px solid var(--wallox-border-color, #E4DACC);
  padding: 0px 22px 0px 10px;
  border-radius: 6px;
}
.product__price-ranger #slider-range {
  margin: 22px 0 0 0px;
  background: var(--wallox-white, #fff);
  border: 1px solid var(--wallox-base, #DF9E42) !important;
  border-radius: 10px !important;
  border: none;
  height: 7px;
  border-radius: 0;
  position: relative;
}
.product__price-ranger #slider-range .ui-slider-range {
  height: 100%;
  background: var(--wallox-base, #DF9E42);
}
.product__price-ranger #slider-range .ui-slider-handle {
  position: absolute;
  top: -6px;
  background: var(--wallox-base, #DF9E42);
  border: 0;
  height: 18px;
  width: 18px !important;
  border-radius: 50%;
  margin-left: -2px;
  outline: medium none;
  cursor: pointer;
  z-index: 2;
}
.product__price-ranger .price-ranger .ui-slider .ui-slider-handle:nth-child(2) {
  display: none;
}
.product__price-ranger .price-ranger .ui-slider .ui-slider-handle:nth-child(3)::after {
  content: "";
  width: 8px;
  height: 8px;
  background-color: var(--wallox-white, #fff);
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.product__price-ranger .ranger-min-max-block {
  position: relative;
  display: block;
  margin: 3px 0 0 0px;
}
.product__price-ranger .ranger-min-max-block input[type=text] {
  position: relative;
  display: inline-block;
  color: var(--wallox-text, #7E7C76);
  font-size: 12px;
  font-weight: 500;
  width: 40px;
  line-height: 20px;
  border: none;
  outline: none;
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  padding: 0;
  text-align: center;
  background-color: transparent;
}
.product__price-ranger .ranger-min-max-block span {
  position: relative;
  display: inline-block;
  color: var(--wallox-text, #7E7C76);
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  left: -2px;
}
.product__categories {
  background-color: var(--wallox-gray, #F4EDE4);
  padding: 30px;
  border-radius: 10px;
}
.product__categories ul {
  margin: 0;
  padding: 0;
  list-style: none;
  margin-left: -15px;
  margin-right: -15px;
  margin-bottom: -20px;
}
.product__categories ul li {
  position: relative;
  margin: 0 0 4px;
}
.product__categories ul li a {
  position: relative;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  z-index: 1;
  padding: 11px 15px 11px;
  font-weight: 500;
  font-size: 16px;
  line-height: 100%;
  text-transform: capitalize;
  color: var(--wallox-text, #7E7C76);
}
.product__categories ul li a i {
  position: relative;
  display: inline-block;
  font-size: 12px;
  color: var(--wallox-text, #7E7C76);
  margin-right: 10px;
  transition: all 0.3s ease;
}
.product__categories ul li a::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 100%;
  background-color: var(--wallox-base, #DF9E42);
  z-index: -1;
  transform: scale(1, 0);
  perspective: 400px;
  visibility: hidden;
  transition: transform 500ms ease-in-out, visibility 500ms ease-in-out;
  transform-origin: bottom center;
}
.product__categories ul li:hover a, .product__categories ul li.active a {
  color: var(--wallox-white, #fff);
  padding-left: 27px;
}
.product__categories ul li:hover a i, .product__categories ul li.active a i {
  color: var(--wallox-white, #fff);
}
.product__categories ul li:hover a::before, .product__categories ul li.active a::before {
  transform: scale(1, 1);
  visibility: visible;
  transform-origin: top center;
}
.product__categories ul li.active a {
  font-weight: 600;
}
.product__info-top {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}
@media (max-width: 991px) {
  .product__info-top {
    margin-top: 50px;
  }
}
@media (max-width: 767px) {
  .product__info-top {
    display: block;
    margin-top: 40px;
  }
}
.product__showing-text {
  margin: 0;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 100%;
  padding-bottom: 0;
  color: var(--wallox-text, #7E7C76);
  text-transform: capitalize;
}
@media (max-width: 767px) {
  .product__showing-text {
    margin-bottom: 10px;
  }
}
.product__showing-sort {
  margin: 0;
  font-size: 18px;
}
.product__showing-sort .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
  position: relative;
  display: block;
  width: 200px !important;
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
}
@media (max-width: 360px) {
  .product__showing-sort .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
    width: 300px !important;
  }
}
.product__showing-sort .bootstrap-select > .dropdown-toggle::after {
  display: none;
}
.product__showing-sort .bootstrap-select .dropdown-menu {
  border: none;
}
.product__showing-sort .bootstrap-select > .dropdown-toggle {
  position: relative;
  height: 56px;
  outline: none !important;
  border-radius: 6px;
  border: 0;
  background-color: var(--wallox-gray, #F4EDE4) !important;
  margin: 0;
  padding: 0;
  padding-left: 15px;
  padding-right: 15px;
  color: var(--wallox-text-dark, #2E2A20) !important;
  box-shadow: none !important;
  background-repeat: no-repeat;
  background-size: 14px 12px;
  background-position: right 25.75px center;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 56px;
  text-transform: capitalize;
}
.product__showing-sort .bootstrap-select > .dropdown-toggle:before {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 15px;
  font-family: "icomoon" !important;
  content: "\e90b";
  font-weight: 900;
  font-size: 14px;
  color: var(--wallox-text-dark, #2E2A20) !important;
}
.product__showing-sort .bootstrap-select .dropdown-menu > li + li > a {
  border-top: 1px solid var(--wallox-border-color, #E4DACC);
}
.product__showing-sort .bootstrap-select .dropdown-menu > li > a {
  font-size: 16px;
  font-weight: 600;
  padding: 10px 30px;
  color: var(--wallox-text-dark, #2E2A20);
  background-color: var(--wallox-gray, #F4EDE4);
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
  text-transform: capitalize;
}
.product__showing-sort .bootstrap-select .dropdown-menu > li:hover > a,
.product__showing-sort .bootstrap-select .dropdown-menu > li.selected > a {
  background: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
  border-color: var(--wallox-base, #DF9E42);
}
.product__item {
  position: relative;
  background-color: var(--wallox-white, #fff);
  transition: all 500ms ease;
}
.product__item:hover {
  box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.05);
}
.product__item__img {
  background-color: var(--wallox-gray, #F4EDE4);
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  text-align: center;
}
.product__item__img img {
  text-align: center;
  width: auto !important;
  mix-blend-mode: multiply;
  transition: all 500ms ease;
  transform: scale(1);
}
.product__item__btn {
  position: absolute;
  right: 20px;
  top: 20px;
  z-index: 2;
}
.product__item__btn a {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background-color: var(--wallox-white, #fff);
  border-radius: 50%;
  color: var(--wallox-text-dark, #2E2A20);
  font-size: 14px;
  visibility: hidden;
  opacity: 0;
}
.product__item__btn a:hover {
  background-color: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
}
.product__item__btn a:nth-child(1) {
  -webkit-transition: transform 350ms cubic-bezier(0.445, 0.05, 0.55, 0.95), opacity ease 300ms, visibility ease 300ms, background ease 300ms, color ease 300ms, border ease 300ms;
  -moz-transition: transform 350ms cubic-bezier(0.445, 0.05, 0.55, 0.95), opacity ease 300ms, visibility ease 300ms, background ease 300ms, color ease 300ms, border ease 300ms;
  -ms-transition: transform 350ms cubic-bezier(0.445, 0.05, 0.55, 0.95), opacity ease 300ms, visibility ease 300ms, background ease 300ms, color ease 300ms, border ease 300ms;
  -o-transition: transform 350ms cubic-bezier(0.445, 0.05, 0.55, 0.95), opacity ease 300ms, visibility ease 300ms, background ease 300ms, color ease 300ms, border ease 300ms;
  transition: transform 350ms cubic-bezier(0.445, 0.05, 0.55, 0.95), opacity ease 300ms, visibility ease 300ms, background ease 300ms, color ease 300ms, border ease 300ms;
  transform: translate3d(30px, 0, 0);
  -moz-transform: translate3d(30px, 0, 0);
  -webkit-transform: translate3d(30px, 0, 0);
  -ms-transform: translate3d(30px, 0, 0);
  -o-transform: translate3d(30px, 0, 0);
}
.product__item__btn a:nth-child(2) {
  -webkit-transition: transform 550ms cubic-bezier(0.445, 0.05, 0.55, 0.95), opacity ease 300ms, visibility ease 300ms, background ease 300ms, color ease 300ms, border ease 300ms;
  -moz-transition: transform 550ms cubic-bezier(0.445, 0.05, 0.55, 0.95), opacity ease 300ms, visibility ease 300ms, background ease 300ms, color ease 300ms, border ease 300ms;
  -ms-transition: transform 550ms cubic-bezier(0.445, 0.05, 0.55, 0.95), opacity ease 300ms, visibility ease 300ms, background ease 300ms, color ease 300ms, border ease 300ms;
  -o-transition: transform 550ms cubic-bezier(0.445, 0.05, 0.55, 0.95), opacity ease 300ms, visibility ease 300ms, background ease 300ms, color ease 300ms, border ease 300ms;
  transition: transform 550ms cubic-bezier(0.445, 0.05, 0.55, 0.95), opacity ease 300ms, visibility ease 300ms, background ease 300ms, color ease 300ms, border ease 300ms;
  transform: translate3d(30px, 0, 0);
  -moz-transform: translate3d(30px, 0, 0);
  -webkit-transform: translate3d(30px, 0, 0);
  -ms-transform: translate3d(30px, 0, 0);
  -o-transform: translate3d(30px, 0, 0);
}
.product__item__btn a + a {
  margin-top: 10px;
}
.product__item:hover .product__item__img img {
  transform: scale(1.05);
}
.product__item:hover .product__item__btn a {
  opacity: 1;
  visibility: visible;
  transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -webkit-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  -o-transform: translate3d(0, 0, 0);
}
.product__item__content {
  position: relative;
  text-align: center;
  padding: 30px 20px;
  border-top: none !important;
  border: 1px solid var(--wallox-border-color, #E4DACC);
}
.product__item__ratings {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  color: var(--wallox-base, #DF9E42);
  letter-spacing: 2.5px;
  margin-bottom: 17px;
}
.product__item__title {
  margin: 0;
  margin-bottom: 13px;
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 26px;
  color: var(--wallox-text-dark, #2E2A20);
  text-transform: uppercase;
}
.product__item__title a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.product__item__title a:hover {
  background-size: 100% 1px;
}
.product__item__title a:hover {
  color: var(--wallox-base, #DF9E42);
}
.product__item__price {
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--wallox-text, #7E7C76);
  font-style: normal;
  font-weight: 500;
  margin-bottom: 15px;
  font-size: 18px;
  line-height: 23px;
}
.product__item__link {
  display: inline-flex;
  align-items: center;
  margin-bottom: 0;
  border-radius: 0;
  font-style: normal;
  font-weight: 700;
  font-size: 12px;
  line-height: 133%;
  text-transform: uppercase;
  color: var(--wallox-white, #fff);
  transform: scale(1);
  padding: 12px 16px;
}
.product__item__link__icon {
  position: relative;
  display: inline-flex;
  margin-left: 30px;
  font-size: 16px;
}
.product__item__link__icon i {
  color: inherit;
}
.product__item__link__icon::before {
  content: "";
  width: 1px;
  height: 100%;
  position: absolute;
  top: 50%;
  left: -15px;
  transform: translateY(-50%);
  background-color: rgba(var(--wallox-white-rgb, 255, 255, 255), 0.3);
  transition: all 400ms ease;
}
.product__item__link::before {
  border-radius: 0;
}
.product__item__link:hover .product__item__link__icon::before {
  background-color: rgba(var(--wallox-base-rgb, 223, 158, 66), 0.2);
}
.product__item__link:hover {
  background: transparent;
}

.product-one {
  padding: 120px 0;
  background-color: var(--wallox-white, #fff);
}
@media (max-width: 767px) {
  .product-one {
    padding: 80px 0;
  }
}
@media (min-width: 992px) {
  .product-one__carousel .owl-nav {
    display: none;
  }
}
.product-one .post-pagination {
  margin-top: 30px;
}

/*--------------------------------------------------------------
# Shop details
--------------------------------------------------------------*/
.product-details {
  position: relative;
  padding: 120px 0;
  background-color: var(--wallox-white, #fff);
}
@media (max-width: 991px) {
  .product-details {
    padding: 100px 0;
  }
}
@media (max-width: 767px) {
  .product-details {
    padding: 80px 0;
  }
}
.product-details__img {
  background-color: var(--wallox-gray, #F4EDE4);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
}
.product-details__img img {
  max-width: 100%;
  height: auto;
  border-radius: 20px;
}
.product-details__top {
  display: flex;
  flex-wrap: wrap;
  align-items: baseline;
  font-family: var(--wallox-heading-font, "Plus Jakarta Sans", serif);
  margin-bottom: 22px;
}
.product-details__title {
  font-size: 34px;
  text-transform: capitalize;
  margin: 0;
  color: var(--wallox-text-dark, #2E2A20);
  font-style: normal;
  font-weight: 700;
  font-size: 24px;
  line-height: 30px;
}
.product-details__price {
  color: var(--wallox-base, #DF9E42);
  margin: 0 0 0 30px;
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  font-weight: 700;
  font-size: 24px;
  line-height: 31px;
}
.product-details__review {
  position: relative;
  display: flex;
  align-items: center;
  letter-spacing: 3px;
  font-size: 16px;
  color: var(--wallox-base, #DF9E42);
}
.product-details__review a {
  display: inline-block;
  color: var(--wallox-text, #7E7C76);
  font-size: 16px;
  letter-spacing: 0;
  font-weight: 400;
  transition: all 500ms ease;
  line-height: 20px;
  text-transform: capitalize;
}
.product-details__review a:hover {
  color: var(--wallox-base, #DF9E42);
}
.product-details__divider {
  width: 100%;
  height: 1px;
  background-color: rgba(var(--wallox-border-color-rgb, 228, 218, 204), 0.4);
  margin: 25px 0 21px;
}
.product-details__excerpt {
  margin: 0;
}
.product-details__excerpt-text1 {
  margin: 0 0 38px;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 188%;
  color: var(--wallox-text, #7E7C76);
}
.product-details__quantity {
  position: relative;
  display: flex;
  align-items: center;
  margin: 0px 0 40px;
}
.product-details__quantity-title {
  margin: 0;
  text-transform: capitalize;
  margin-right: 20px;
  font-style: normal;
  font-weight: 700;
  font-size: 16px;
  line-height: 175%;
}
.product-details__quantity .quantity-box {
  position: relative;
  width: 98px;
  height: 50px;
}
.product-details__quantity .quantity-box input {
  width: 98px;
  height: 50px;
  border: 1px solid var(--wallox-border-color, #E4DACC);
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  appearance: textfield;
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  color: var(--wallox-base, #DF9E42);
  padding-left: 30px;
  outline: none;
  font-size: 18px;
  font-weight: 500;
  background-color: transparent;
}
.product-details__quantity .quantity-box button {
  width: 24px;
  height: 24px;
  color: var(--wallox-text, #7E7C76);
  font-size: 12px;
  position: absolute;
  top: 1px;
  right: 1px;
  background-color: transparent;
  border: none;
  border-left: 1px solid var(--wallox-border-color, #E4DACC);
  display: flex;
  align-items: center;
  justify-content: center;
  outline: none;
  transition: all 500ms ease;
}
.product-details__quantity .quantity-box button.sub {
  bottom: 1px;
  top: auto;
  border-top: 1px solid var(--wallox-border-color, #E4DACC);
}
.product-details__quantity .quantity-box button:hover {
  color: var(--wallox-base, #DF9E42);
}
.product-details__buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}
.product-details__buttons a {
  text-transform: uppercase;
  display: inline-flex;
  font-style: normal;
  font-weight: 700;
  font-size: 15px;
  line-height: 20px;
  margin-bottom: 0;
  padding: 16.5px 22px;
  border-radius: 0;
  transform: scale(1);
  color: var(--wallox-white, #fff);
}
.product-details__buttons a::before {
  border-radius: 0;
}
.product-details__buttons a:last-child {
  border: 1px solid var(--wallox-text-dark, #2E2A20);
  padding: 16.5px 30px;
  background: var(--wallox-text-dark, #2E2A20);
  color: var(--wallox-white, #fff);
}
.product-details__buttons a:last-child:hover {
  color: var(--wallox-white, #fff);
  border: 1px solid var(--wallox-base, #DF9E42);
  background: var(--wallox-base, #DF9E42);
}
.product-details__buttons__icon {
  position: relative;
  display: inline-flex;
  margin-left: 29px;
  font-size: 18px;
}
.product-details__buttons__icon i {
  font-size: 18px;
}
.product-details__buttons__icon::before {
  content: "";
  width: 1px;
  height: 100%;
  position: absolute;
  top: 50%;
  left: -14px;
  transform: translateY(-50%);
  background-color: rgba(var(--wallox-white-rgb, 255, 255, 255), 0.2);
  transition: all 400ms ease;
}
.product-details__socials {
  position: relative;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 40px;
}
.product-details__socials__title {
  text-transform: capitalize;
  margin: 0;
  margin-right: 10px;
  flex: 0 0 100%;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 100%;
  color: var(--wallox-text, #7E7C76);
}
@media (min-width: 768px) {
  .product-details__socials__title {
    flex: 0 0 auto;
  }
}
.product-details__socials a {
  width: 36px;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: transparent;
  font-size: 16px;
  color: var(--wallox-text, #7E7C76);
  transition: all 500ms ease;
  border: 1px solid var(--wallox-border-color, #E4DACC);
}
.product-details__socials a:hover {
  background-color: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
}
.product-details__description {
  position: relative;
  margin: 55px 0 58px;
  border-bottom: 1px solid var(--wallox-border-color, #E4DACC);
}
@media (max-width: 991px) {
  .product-details__description {
    margin: 45px 0 30px;
  }
}
.product-details__description__title {
  font-size: 24px;
  text-transform: capitalize;
  margin-bottom: 18px;
  font-weight: bold;
  line-height: 31px;
}
.product-details__description__text {
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 188%;
}
.product-details__description__text + .product-details__description__text {
  margin-top: 23px;
  padding-bottom: 20px;
}
.product-details__description__lists {
  margin: 0 0 30px;
  padding: 0;
}
.product-details__description__lists li {
  display: block;
  position: relative;
  padding: 0 0 0 36px;
  font-size: 16px;
  line-height: 30px;
  font-weight: 600;
  color: var(--wallox-text-dark, #2E2A20);
}
.product-details__description__lists li span {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  display: inline-block;
  font-size: 20px;
  line-height: 30px;
  color: var(--wallox-base, #DF9E42);
}
.product-details__review-title {
  margin-bottom: 44px;
  font-weight: bold;
  font-style: normal;
  font-size: 24px;
  line-height: 154%;
}
.product-details__comment-title {
  margin: 0;
  text-transform: capitalize;
  font-size: 25px;
  margin-top: -4px;
  margin-bottom: 35px;
  font-style: normal;
  font-weight: 600;
  line-height: 100%;
  color: var(--wallox-text-dark, #2E2A20);
}
@media (min-width: 992px) {
  .product-details__comment-title {
    margin-bottom: 56px;
  }
}
.product-details__comment__list {
  margin: 0;
  margin-top: 35px;
}
.product-details__comment__card {
  margin-bottom: 30px;
  padding-bottom: 27px;
  border-bottom: 1px solid var(--wallox-border-color, #E4DACC);
}
@media (min-width: 768px) {
  .product-details__comment__card {
    display: flex;
    align-items: flex-start;
  }
}
.product-details__comment__card__top {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.product-details__comment__card__image {
  margin-right: 20px;
}
.product-details__comment__card__image img {
  border-radius: 50%;
}
.product-details__comment__card__title {
  margin: 0;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
  margin-top: 20px;
  font-style: normal;
  font-weight: 600;
  font-size: 20px;
  line-height: 110%;
  margin-bottom: 15px;
}
.product-details__comment__card__date {
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 100%;
  margin-bottom: 20px;
  padding-bottom: 0;
  color: var(--wallox-text, #7E7C76);
  text-shadow: 0 0 0.2px currentColor;
}
.product-details__comment__card__text {
  margin-bottom: 20px;
  font-weight: 500;
  font-size: 16px;
  line-height: 188%;
  color: var(--wallox-text, #7E7C76);
  margin-bottom: -7px;
  padding-bottom: 0;
}
.product-details__comment__card__star {
  color: var(--wallox-base, #DF9E42);
  font-size: 16px;
  letter-spacing: 2px;
}
.product-details__comment__card__content {
  position: relative;
}
.product-details__form {
  position: relative;
  margin: 51px 0 0;
}
@media (max-width: 767px) {
  .product-details__form {
    margin: 30px 0 0;
  }
}
.product-details__form .row {
  --bs-gutter-x: 20px;
}
.product-details__form-title {
  margin-bottom: 14px;
  font-weight: bold;
  font-style: normal;
  font-size: 24px;
  line-height: 154%;
  text-transform: none;
}
.product-details__form-text {
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 100%;
  margin-bottom: 27px;
  padding-bottom: 0;
  text-transform: capitalize;
}
.product-details__form-ratings {
  display: flex;
  align-items: center;
  letter-spacing: 6px;
  font-size: 16px;
  color: var(--wallox-base, #DF9E42);
  margin: 0 0 15px;
}
.product-details__form-ratings__label {
  display: inline-block;
  letter-spacing: 0;
  color: var(--wallox-text-dark, #2E2A20);
  margin: 0 17px 0 0;
  font-style: normal;
  font-weight: 700;
  font-size: 16px;
  line-height: 100%;
  text-transform: capitalize;
}
.product-details__form-ratings span {
  color: var(--wallox-text, #7E7C76);
}
.product-details__form__form {
  margin-top: 30px;
}
.product-details__form__form input[type=text],
.product-details__form__form input[type=email] {
  background-color: transparent;
  color: var(--wallox-text, #7E7C76);
  font-size: 16px;
  font-weight: 500;
  border-top: none;
  border-left: none;
  border-right: none;
  border-bottom: 1px solid var(--wallox-border-color, #E4DACC);
  outline: none;
  padding-left: 0px;
  padding-right: 30px;
}
.product-details__form__form input[type=text]::placeholder,
.product-details__form__form input[type=email]::placeholder {
  color: var(--wallox-text, #7E7C76);
  font-size: 16px;
  font-weight: 500;
  text-transform: capitalize;
}
.product-details__form .wallox-btn {
  font-style: normal;
  font-weight: 700;
  font-size: 16px;
  line-height: 21px;
  text-transform: uppercase;
  padding: 12.5px 22.5px;
  color: var(--wallox-white, #fff);
  border-radius: 6px;
}
.product-details__form .wallox-btn::before {
  border-radius: 6px;
}

/*--------------------------------------------------------------
# Cart
--------------------------------------------------------------*/
.cart-page {
  position: relative;
  padding: 120px 0;
}
@media (max-width: 767px) {
  .cart-page {
    padding: 80px 0;
  }
}
.cart-page .table-responsive {
  position: relative;
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}
.cart-page__table {
  position: relative;
  width: 100%;
  border: none;
  margin: 0 0 60px;
}
@media (max-width: 1199px) {
  .cart-page__table {
    min-width: 1170px;
  }
}
.cart-page__table thead tr th {
  color: var(--wallox-text-dark, #2E2A20);
  font-size: 20px;
  line-height: 20px;
  font-weight: 600;
  padding: 0 0 24px;
  font-family: var(--wallox-heading-font, "Plus Jakarta Sans", serif);
  text-transform: capitalize;
  border: none;
  background-color: transparent;
  border-bottom: 1px solid var(--wallox-border-color, #E4DACC) !important;
  box-shadow: none;
}
.cart-page__table thead tr th:last-child {
  text-align: right;
}
.cart-page__table tbody tr td {
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 21px;
  color: var(--wallox-text, #7E7C76);
  vertical-align: middle;
  border: none;
  box-shadow: none;
  background-color: transparent;
  border-top: 1px solid var(--wallox-border-color, #E4DACC);
  border-bottom: 1px solid var(--wallox-border-color, #E4DACC);
  padding: 30px 0;
  letter-spacing: 0;
}
.cart-page__table tbody tr td:last-child {
  text-align: right;
}
.cart-page__table__meta {
  display: flex;
  align-items: center;
}
.cart-page__table__meta-img {
  width: 70px;
  height: 70px;
  background-color: var(--wallox-gray, #F4EDE4);
  margin-right: 10px;
}
.cart-page__table__meta-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  mix-blend-mode: multiply;
  border-radius: 6px;
}
.cart-page__table__meta-title {
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 23px;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
  margin-bottom: 0;
  padding-bottom: 0;
}
.cart-page__table__meta-title a {
  color: inherit;
}
.cart-page__table__meta-title a:hover {
  color: var(--wallox-base, #DF9E42);
}
.cart-page__table__remove {
  display: block;
  color: var(--wallox-text, #7E7C76);
  font-size: 13px;
  padding-right: 20px;
}
.cart-page__table__remove:hover {
  color: var(--wallox-base, #DF9E42);
}
.cart-page__table .product-details__quantity {
  margin-bottom: 0;
}
.cart-page__table .product-details__quantity .quantity-box input {
  font-size: 16px;
  font-weight: bold;
}
.cart-page__coupone__box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media (max-width: 767px) {
  .cart-page__coupone__box {
    margin-top: 20px;
    justify-content: start;
    flex-direction: column;
    gap: 30px;
    align-items: start;
  }
}
.cart-page__coupone__box__btn {
  text-transform: uppercase;
  padding: 17px 28px;
  border-radius: 6px;
  color: var(--wallox-base, #DF9E42);
  border: 1px solid var(--wallox-base, #DF9E42);
  background: transparent;
}
.cart-page__coupone__box__btn::before {
  border-radius: 6px;
}
.cart-page__coupone {
  margin-bottom: 50px;
}
.cart-page__coupone__title {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 21px;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
  text-shadow: 0 0 0.2px currentColor;
}
.cart-page__coupone__form {
  position: relative;
  display: flex;
}
@media (max-width: 767px) {
  .cart-page__coupone__form {
    display: block;
    width: 100%;
  }
}
.cart-page__coupone__form input[type=text] {
  height: 58px;
  width: 268px;
  border: none;
  background-color: transparent;
  padding-left: 30px;
  padding-right: 30px;
  outline: none;
  border: 1px solid var(--wallox-border-color, #E4DACC);
  font-size: 16px;
  color: var(--wallox-text, #7E7C76);
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  display: block;
  font-weight: 500;
  margin-right: 10px;
  border-radius: 6px;
}
.cart-page__coupone__form input[type=text]::placeholder {
  text-transform: capitalize;
  font-size: 16px;
  text-transform: capitalize;
}
@media (max-width: 1199px) {
  .cart-page__coupone__form input[type=text] {
    width: 250px;
  }
}
@media (max-width: 767px) {
  .cart-page__coupone__form input[type=text] {
    width: 100%;
    margin: 0 0 10px;
  }
}
.cart-page__coupone__form button {
  text-transform: uppercase;
  border-radius: 6px;
  color: var(--wallox-white, #fff);
}
.cart-page__coupone__form button::before {
  border-radius: 6px;
}
.cart-page__coupone__list {
  max-width: 315px;
  margin-left: auto;
  margin-right: 0;
  width: 100%;
}
.cart-page__cart-total__title {
  font-style: normal;
  font-weight: 700;
  font-size: 30px;
  line-height: 39px;
  border-bottom: 1px solid var(--wallox-border-color, #E4DACC);
  padding-bottom: 20px;
  margin-bottom: 25px;
}
.cart-page__cart-total {
  margin-bottom: 10px;
}
.cart-page__cart-total__item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-style: normal;
  font-weight: 700;
  font-size: 16px;
  line-height: 150%;
  text-transform: capitalize;
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  color: var(--wallox-text-dark, #2E2A20);
}
.cart-page__cart-total__item:first-child {
  margin-bottom: 15px;
}
.cart-page__cart-total__item:last-child {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid var(--wallox-border-color, #E4DACC);
}
.cart-page__cart-total__item:last-child span {
  color: var(--wallox-text-dark, #2E2A20);
}
.cart-page__cart-total__item span {
  font-style: normal;
  font-weight: 600;
  font-size: 20px;
  line-height: 160%;
  color: var(--wallox-text-dark, #2E2A20);
}
.cart-page__cart-total__item p {
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 162%;
  margin-bottom: 0;
  padding-bottom: 0;
}
.cart-page__buttons {
  display: flex;
  justify-content: end;
}
.cart-page__buttons a {
  padding: 17px 28px;
  text-transform: uppercase;
  border-radius: 6px;
  color: var(--wallox-white, #fff);
}
.cart-page__buttons a::before {
  border-radius: 6px;
}

/*--------------------------------------------------------------
# Checkout
--------------------------------------------------------------*/
.checkout-page {
  position: relative;
  padding: 120px 0;
  background-color: var(--wallox-white, #fff);
}
@media (max-width: 767px) {
  .checkout-page {
    padding: 80px 0;
  }
}
.checkout-page .bs-gutter-x-20 {
  --bs-gutter-x: 20px;
}
.checkout-page__billing-address {
  position: relative;
}
.checkout-page__billing-address__title {
  text-transform: capitalize;
  margin: 0 0 34px;
  font-style: normal;
  font-weight: 700;
  font-size: 24px;
  line-height: 31px;
  color: var(--wallox-text-dark, #2E2A20);
}
@media (max-width: 767px) {
  .checkout-page__billing-address__title {
    font-size: 20px;
  }
}
.checkout-page__shipping-address {
  position: relative;
}
@media (max-width: 991px) {
  .checkout-page__shipping-address {
    margin: 50px 0 0;
  }
}
.checkout-page__shipping-address .checkout-page__form {
  margin-top: 50px;
}
.checkout-page__shipping-address__title {
  text-transform: capitalize;
  margin: 0 0 18px;
  font-style: normal;
  font-weight: 500;
  font-size: 24px;
  line-height: 31px;
}
@media (max-width: 767px) {
  .checkout-page__shipping-address__title {
    font-size: 20px;
  }
}
.checkout-page__input-box {
  position: relative;
  line-height: 1;
  margin: 0 0 20px;
}
.checkout-page__input-box label {
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 21px;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
  text-shadow: 0 0 0.2px currentColor;
  margin-bottom: 13px;
}
.checkout-page__input-box input[type=text],
.checkout-page__input-box input[type=email],
.checkout-page__input-box input[type=tel] {
  border-radius: 6px;
  height: 60px;
  width: 100%;
  border: none;
  background-color: var(--wallox-gray, #F4EDE4);
  padding-left: 20px;
  padding-right: 20px;
  outline: none;
  font-size: 14px;
  color: var(--wallox-text, #7E7C76);
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  display: block;
  font-weight: 500;
}
.checkout-page__input-box .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
  position: relative;
  display: block;
  width: 100% !important;
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
}
.checkout-page__input-box .bootstrap-select > .dropdown-toggle::after {
  display: none;
}
.checkout-page__input-box .bootstrap-select > .dropdown-toggle {
  position: relative;
  height: 60px;
  outline: none !important;
  border-radius: 6px;
  border: 0;
  background-color: var(--wallox-gray, #F4EDE4) !important;
  margin: 0;
  padding: 0;
  padding-left: 20px;
  padding-right: 20px;
  color: var(--wallox-text, #7E7C76) !important;
  font-size: 14px;
  line-height: 58px;
  font-weight: 500;
  box-shadow: none !important;
  background-repeat: no-repeat;
  text-transform: capitalize;
  background-size: 14px 12px;
  background-position: right 25.75px center;
}
.checkout-page__input-box .bootstrap-select > .dropdown-toggle:before {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 30px;
  font-family: "icomoon" !important;
  content: "\e90b";
  font-weight: 900;
  font-size: 14px;
  color: var(--wallox-text, #7E7C76);
}
.checkout-page__input-box .bootstrap-select .dropdown-menu > li + li > a {
  border-top: 1px solid var(--wallox-border-color, #E4DACC);
}
.checkout-page__input-box .bootstrap-select .dropdown-menu {
  border: none;
}
.checkout-page__input-box .bootstrap-select .dropdown-menu > li > a {
  font-size: 14px;
  font-weight: 500;
  padding: 15px 30px;
  color: var(--wallox-text, #7E7C76);
  background-color: var(--wallox-gray, #F4EDE4);
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.checkout-page__input-box .bootstrap-select .dropdown-menu > li:hover > a,
.checkout-page__input-box .bootstrap-select .dropdown-menu > li.selected > a {
  background: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
  border-color: var(--wallox-base, #DF9E42);
}
.checkout-page__input-box textarea {
  color: var(--wallox-text, #7E7C76);
  height: 177px;
  width: 100%;
  background-color: var(--wallox-gray, #F4EDE4);
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  padding: 20px 30px 30px;
  border: none;
  border-radius: 6px;
  outline: none;
  margin-bottom: 0px;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 162%;
  text-transform: capitalize;
}
.checkout-page__your-order {
  position: relative;
  margin: 32px 0 0;
}
.checkout-page__your-order__title {
  text-transform: capitalize;
  margin-bottom: 27px;
  font-style: normal;
  font-weight: 700;
  font-size: 24px;
  line-height: 31px;
  color: var(--wallox-text-dark, #2E2A20);
}
.checkout-page__order-table {
  position: relative;
  width: 100%;
  border: none;
  margin: 0 0 0;
}
.checkout-page__order-table thead tr th {
  font-size: 20px;
  color: var(--wallox-text-dark, #2E2A20);
  text-transform: capitalize;
  font-style: normal;
  font-weight: 500;
  font-size: 20px;
  line-height: 26px;
  font-family: var(--wallox-heading-font, "Plus Jakarta Sans", serif);
  margin: 0;
  padding: 28px 0;
  text-shadow: 0 0 0.1px currentColor;
  border: none;
  border-top: 1px solid var(--wallox-border-color, #E4DACC);
  border-bottom: 1px solid var(--wallox-border-color, #E4DACC);
}
.checkout-page__order-table thead tr th:last-child {
  text-align: right;
}
.checkout-page__order-table tbody tr td {
  color: var(--wallox-text, #7E7C76);
  margin: 0;
  padding: 0 0 30px;
  border: none;
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 21px;
  text-transform: capitalize;
}
.checkout-page__order-table tbody tr td:last-child {
  text-align: right;
}
.checkout-page__order-table tbody tr:last-child {
  border-top: 1px solid var(--wallox-border-color, #E4DACC);
}
.checkout-page__order-table tbody tr:last-child td {
  padding-top: 20px;
  color: var(--wallox-text-dark, #2E2A20);
  text-shadow: 0 0 0.1px currentColor;
}
.checkout-page__order-table tbody tr:first-child td {
  padding-top: 25px;
}
.checkout-page__order-table tbody tr:last-child td {
  padding-bottom: 26px;
}
.checkout-page__payment {
  background-color: var(--wallox-gray, #F4EDE4);
  padding: 30px;
  min-height: 295px;
}
.checkout-page__payment__item {
  position: relative;
}
.checkout-page__payment__title {
  display: flex;
  text-transform: capitalize;
  margin: 0;
  align-items: center;
  margin-bottom: 20px;
  cursor: pointer;
  color: var(--wallox-text-dark, #2E2A20);
  font-style: normal;
  font-weight: 700;
  font-size: 16px;
  line-height: 21px;
}
.checkout-page__payment__title::before {
  content: "\f111";
  width: 20px;
  height: 20px;
  background-color: transparent;
  border-radius: 50%;
  margin-right: 10px;
  border: 1px solid var(--wallox-text, #7E7C76);
  border-radius: 50%;
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  font-size: 8px;
  display: flex;
  justify-content: center;
  color: var(--wallox-text, #7E7C76);
  align-items: center;
  margin-right: 14px;
  position: relative;
  top: 1px;
  transition: all 500ms ease;
}
.checkout-page__payment__item--active .checkout-page__payment__title::before {
  background-color: transparent;
  border-color: var(--wallox-base, #DF9E42);
  content: "\f111";
  color: var(--wallox-base, #DF9E42);
}
.checkout-page__payment__content {
  margin-left: 35px;
  margin-bottom: 28px;
  font-size: 15px;
  line-height: 30px;
}
.checkout-page__payment .wallox-btn {
  margin-top: 40px;
  text-transform: uppercase;
  padding: 17px 28px;
  border-radius: 6px;
  color: var(--wallox-white, #fff);
}
.checkout-page__payment .wallox-btn::before {
  border-radius: 6px;
}

/*--------------------------------------------------------------
# Login
--------------------------------------------------------------*/
.login-page {
  padding: 120px 0px;
  position: relative;
}
.login-page__left {
  position: relative;
  z-index: 1;
}
.login-page__thumb {
  position: relative;
  z-index: 1;
  margin-right: 100px;
}
.login-page__thumb img {
  width: 100%;
  object-fit: cover;
  z-index: 1;
  position: relative;
}
.login-page__thumb-two {
  position: absolute;
  right: 0;
  bottom: 70px;
  z-index: 1;
}
.login-page__thumb-two img {
  border: 10px solid var(--wallox-white, #fff);
  border-radius: 20px;
}
.login-page__login-box {
  position: relative;
  background: var(--wallox-gray, #F4EDE4);
  padding: 55px 105px;
  border-radius: 30px;
}
.login-page__content {
  max-width: 557px;
  margin-left: auto;
  margin-right: auto;
}
.login-page__logo {
  display: flex;
  width: 100%;
  margin-bottom: 32px;
}
.login-page__title {
  font-style: normal;
  font-weight: 600;
  font-size: 20px;
  line-height: 140%;
  margin-bottom: 10px;
  padding-bottom: 0;
  text-transform: capitalize;
  text-shadow: 0 0 0.1px currentColor;
}
.login-page__input-box {
  position: relative;
}
.login-page__input-box--bottom {
  padding-top: 20px;
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.login-page__input-box__inner {
  display: flex;
  align-items: center;
  gap: 8px;
}
.login-page__input-box__inner .remember-policy {
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 167%;
  letter-spacing: 0.3px;
  color: var(--wallox-text, #7E7C76);
}
.login-page__input-box__toggle {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 40px;
  height: 20px;
  display: inline-block;
  position: relative;
  border-radius: 50px;
  overflow: hidden;
  outline: none;
  border: none;
  cursor: pointer;
  background-color: var(--wallox-border-color, #E4DACC);
  transition: background-color ease 0.3s;
}
.login-page__input-box__toggle::before {
  content: "";
  display: block;
  position: absolute;
  z-index: 2;
  width: 16px;
  height: 16px;
  background: #fff;
  left: 2px;
  top: 2px;
  border-radius: 50%;
  white-space: nowrap;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  transition: all cubic-bezier(0.3, 1.5, 0.7, 1) 0.3s;
}
.login-page__input-box__toggle:checked {
  background-color: var(--wallox-base, #DF9E42);
}
.login-page__input-box__toggle:checked::before {
  left: 22px;
}
.login-page__input-box label {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 86%;
  letter-spacing: 0.3px;
  color: var(--wallox-text-dark, #2E2A20);
  text-shadow: 0 0 0.1px currentColor;
  text-transform: capitalize;
}
.login-page__input-box input[type=text],
.login-page__input-box input[type=password] {
  margin-top: -3px;
  height: 48px;
  width: 100%;
  background-color: var(--wallox-white, #fff);
  padding-left: 15px;
  padding-right: 30px;
  outline: none;
  font-size: 15px;
  color: var(--wallox-text, #7E7C76);
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  display: block;
  font-weight: 500;
  border-radius: 6px;
  border: none;
}
.login-page__input-box input[type=text]::placeholder,
.login-page__input-box input[type=password]::placeholder {
  color: var(--wallox-text, #7E7C76);
  font-style: normal;
  font-weight: 400;
  font-size: 15px;
  line-height: 133%;
  text-transform: none;
}
.login-page__input-box span {
  cursor: pointer;
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(50%);
  z-index: 1;
  font-size: 16px;
  color: var(--wallox-text, #7E7C76);
  transition: all 400ms ease;
}
.login-page__input-box span:hover {
  color: var(--wallox-base, #DF9E42);
}
.login-page__input-box__btn button {
  width: 100%;
  display: block;
  padding-top: 10px;
  padding-bottom: 10px;
}
.login-page__input-box__btn .wallox-btn {
  border-radius: 6px;
  padding-top: 10px;
  padding-bottom: 10px;
  display: flex;
  align-self: center;
  gap: 10px;
  justify-content: center;
}
.login-page__input-box__btn .wallox-btn::before {
  border-radius: 6px;
}
.login-page__input-box__btn .wallox-btn--base {
  border-radius: 6px;
  padding-top: 10px;
  padding-bottom: 10px;
}
.login-page__input-box__btn .wallox-btn--base::before {
  border-radius: 6px;
}
.login-page__input-box__btn:last-child button {
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 167%;
  display: flex;
  align-items: center;
  text-align: center;
}
.login-page__input-box__btn + .login-page__input-box__btn {
  padding-top: 30px;
  margin-top: 30px;
  border-top: 1px solid var(--wallox-border-color, #E4DACC);
}
.login-page .login-page__input-box + .login-page__input-box {
  margin-top: 5px;
}
.login-page__form__forgot {
  font-weight: 400;
  font-size: 12px;
  line-height: 167%;
  text-align: right;
  color: var(--wallox-text, #7E7C76);
  text-transform: capitalize;
  margin-bottom: 0;
  padding-bottom: 0;
}
.login-page__form__forgot:hover {
  color: var(--wallox-base, #DF9E42);
}
.login-page__form__text {
  margin-top: 24px;
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 167%;
  letter-spacing: 0.3px;
  color: #1A1A1A;
  text-transform: capitalize;
  text-align: center;
  margin-bottom: 0;
}
.login-page__form__text a {
  color: var(--wallox-base, #DF9E42);
  margin-left: 5px;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.login-page__form__text a:hover {
  background-size: 100% 1px;
}

.gutter-y-60 {
  --bs-gutter-y: 60px;
}

.gutter-x-60 {
  --bs-gutter-x: 60px;
}

/*--------------------------------------------------------------
# error 404
--------------------------------------------------------------*/
.error-404 {
  padding-bottom: 120px;
  text-align: center;
  padding-top: 120px;
}
@media (max-width: 991px) {
  .error-404 {
    padding: 100px 0px;
  }
}
@media (max-width: 768px) {
  .error-404 {
    padding: 80px 0px;
  }
}
.error-404__thumb {
  position: relative;
  max-width: 720px;
  margin-left: auto;
  margin-right: auto;
  width: 100%;
}
.error-404__thumb__item {
  margin-bottom: 60px;
}
.error-404__thumb__item img {
  width: 100%;
  height: auto;
}
.error-404__thumb__item__moon {
  position: absolute;
  top: 0%;
  left: 15%;
  animation: scale2 4s ease-in-out infinite;
}
.error-404__thumb__item__moon img {
  width: 100%;
  height: auto;
}
.error-404__thumb__item__earth {
  position: absolute;
  bottom: 5%;
  right: 5%;
  animation: svgRotate 7s ease-in-out infinite;
}
.error-404__thumb__item__earth img {
  width: 100%;
  height: auto;
}
.error-404__thumb__item__roket {
  position: absolute;
  bottom: 4%;
  left: 4%;
  animation: topAniLong 4s ease-in-out infinite;
}
.error-404__thumb__item__roket img {
  width: 100%;
  height: auto;
}
.error-404__sub-title {
  font-style: normal;
  font-weight: 600;
  font-size: 30px;
  line-height: 38px;
  text-transform: capitalize;
  padding-bottom: 0;
  margin-bottom: 13px;
  color: var(--wallox-text-dark, #2E2A20);
  text-shadow: 0 0 0.1px currentColor;
}
.error-404__text {
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 175%;
  text-align: center;
  text-transform: capitalize;
  color: var(--wallox-text, #7E7C76);
  max-width: 470px;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 33px;
}
.error-404__btns .wallox-btn {
  border-radius: 10px;
  text-transform: uppercase;
  padding: 13px 25px;
  color: var(--wallox-white, #fff);
}
.error-404__btns .wallox-btn::before {
  border-radius: 10px;
}

/*--------------------------------------------------------------
# Faq
--------------------------------------------------------------*/
.faq-page-search {
  padding: 120px 0px;
  background-color: var(--wallox-white, #fff);
  position: relative;
}
@media (max-width: 991px) {
  .faq-page-search {
    padding: 100px 0px;
  }
}
@media (max-width: 767px) {
  .faq-page-search {
    padding: 80px 0px;
  }
}
.faq-page-search__inner {
  max-width: 770px;
  margin-left: auto;
  margin-right: auto;
  width: 100%;
}
.faq-page-search__top__title {
  text-align: center;
  margin-bottom: 0;
  padding-bottom: 0;
  color: var(--wallox-text-dark, #2E2A20);
  font-style: normal;
  font-weight: 700;
  font-size: 45px;
  line-height: 133%;
  text-align: center;
  letter-spacing: -0.03em;
  text-transform: capitalize;
}
.faq-page-search__form {
  display: flex;
  align-items: center;
  position: relative;
  justify-content: center;
  margin-top: 25px;
  border: 1px solid var(--wallox-border-color, #E4DACC);
  border-radius: 10px;
}
.faq-page-search__form input[type=text] {
  border: none;
  outline: none;
  display: block;
  background-color: var(--wallox-white, #fff);
  color: var(--wallox-text, #7E7C76);
  font-size: 16px;
  width: 100%;
  padding-left: 30px;
  height: 55px;
  border-radius: 10px;
}
.faq-page-search__form input[type=text]::placeholder {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  color: var(--wallox-text, #7E7C76);
}
.faq-page-search__form__btn {
  border: none;
  outline: none;
  background-color: transparent;
  position: absolute;
  top: 50%;
  right: 15px;
  transform: translateY(-50%);
  font-size: 18px;
  transition: all 0.4s ease-in-out;
  color: var(--wallox-base, #DF9E42);
}
@media (min-width: 768px) {
  .faq-page-search__form__btn {
    font-size: 20px;
  }
}
.faq-page-search__form__btn:hover {
  color: var(--wallox-text-dark, #2E2A20);
}

.faq-page {
  padding: 120px 0px;
  background-color: var(--wallox-gray, #F4EDE4);
}
@media (max-width: 991px) {
  .faq-page {
    padding: 100px 0px;
  }
}
@media (max-width: 767px) {
  .faq-page {
    padding: 80px 0px;
  }
}
.faq-page__thumb {
  margin-right: 30px;
  margin-bottom: 30px;
}
.faq-page__thumb img {
  border-radius: 100px;
  object-fit: cover;
  width: 100%;
}
@media (max-width: 1199px) {
  .faq-page__thumb {
    margin-right: 0;
  }
}
.faq-page__list {
  display: flex;
  align-items: start;
  gap: 20px;
}
.faq-page__list__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 80px;
  width: 100%;
  height: 80px;
  border-radius: 50%;
  background: var(--wallox-white, #fff);
  color: var(--wallox-base, #DF9E42);
  font-size: 35px;
  position: relative;
  transition: all 0.4s ease;
  z-index: 1;
}
.faq-page__list__icon::after {
  content: "";
  position: absolute;
  top: 0%;
  height: 0;
  width: 0;
  left: 50%;
  background-color: var(--wallox-base, #DF9E42);
  border-radius: 50%;
  transition: all 0.4s ease;
  z-index: -1;
}
.faq-page__list__title {
  font-style: normal;
  font-weight: 700;
  font-size: 16px;
  line-height: 175%;
  margin-bottom: 13px;
  padding-bottom: 0;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
  text-shadow: 0 0 0.1px currentColor;
}
.faq-page__list__text {
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 162%;
  color: var(--wallox-text, #7E7C76);
  max-width: 410px;
  width: 100%;
}
.faq-page__list:hover .faq-page__list__icon {
  color: var(--wallox-white, #fff);
}
.faq-page__list:hover .faq-page__list__icon::after {
  top: 0%;
  height: 100%;
  width: 100%;
  left: 0%;
}
.faq-page__list + .faq-page__list {
  margin-top: 30px;
}
.faq-page__accordion .accrodion {
  background: var(--wallox-white, #fff);
  border-radius: 100px;
  transition: all 0.2s ease-in-out;
}
.faq-page__accordion .accrodion + .accrodion {
  margin-top: 20px;
}
.faq-page__accordion .accrodion.active {
  border-radius: 20px;
}
.faq-page__accordion .accrodion-title {
  padding: 25px 20px 25px 13px;
  padding-left: 75px;
  cursor: pointer;
}
@media (min-width: 768px) {
  .faq-page__accordion .accrodion-title {
    padding-left: 75px;
  }
}
.faq-page__accordion .accrodion-title .accrodion-title__text {
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
  margin: 0;
  transition: all 500ms ease;
  position: relative;
  font-style: normal;
  font-weight: 700;
  font-size: 16px;
  line-height: 20px;
  position: relative;
}
.faq-page__accordion .accrodion-title__icon {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  background: var(--wallox-text-dark, #2E2A20);
  left: -60px;
  transform: translateY(-50%);
  z-index: 1;
}
.faq-page__accordion .accrodion-title__icon::after, .faq-page__accordion .accrodion-title__icon::before {
  width: 2px;
  height: 16px;
  position: absolute;
  background-color: var(--wallox-white, #fff);
  top: 50%;
  left: 50%;
  content: "";
  transform: translate(-50%, -50%);
  transition: all 500ms ease;
}
.faq-page__accordion .accrodion-title__icon::after {
  width: 16px;
  height: 2px;
}
.faq-page__accordion .active .accrodion-title h4 {
  color: var(--wallox-base, #DF9E42);
}
.faq-page__accordion .active .accrodion-title h4::after {
  width: 109%;
}
.faq-page__accordion .active .accrodion-title__icon {
  background: var(--wallox-base, #DF9E42);
}
.faq-page__accordion .active .accrodion-title__icon::after, .faq-page__accordion .active .accrodion-title__icon::before {
  background-color: var(--wallox-white, #fff);
  opacity: 0;
}
.faq-page__accordion .active .accrodion-title__icon::after {
  opacity: 1;
}
.faq-page__accordion .accrodion-content .inner {
  padding: 7px 30px 24px 20px;
}
.faq-page__accordion .accrodion-content p {
  margin: 0;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 188%;
  color: var(--wallox-text, #7E7C76);
  margin-bottom: -3px;
  text-transform: capitalize;
}

.faqs-two {
  padding: 120px 0px;
  position: relative;
  background-color: var(--wallox-white, #fff);
}
@media (max-width: 991px) {
  .faqs-two {
    padding: 100px 0px;
  }
}
@media (max-width: 767px) {
  .faqs-two {
    padding: 80px 0px;
  }
}
.faqs-two .faq-page__accordion--faqs-two .accrodion {
  background: var(--wallox-gray, #F4EDE4);
}
.faqs-two .faq-page__accordion--faqs-two .active {
  background: var(--wallox-base, #DF9E42);
}
.faqs-two .faq-page__accordion--faqs-two .active .accrodion-title__icon {
  background: var(--wallox-text-dark, #2E2A20);
}
.faqs-two .faq-page__accordion--faqs-two .active .accrodion-title__icon::after, .faqs-two .faq-page__accordion--faqs-two .active .accrodion-title__icon::before {
  background-color: var(--wallox-white, #fff);
  opacity: 0;
}
.faqs-two .faq-page__accordion--faqs-two .active .accrodion-title__icon::after {
  opacity: 1;
}
.faqs-two .faq-page__accordion--faqs-two .active .accrodion-title h4 {
  color: var(--wallox-text-dark, #2E2A20);
}
.faqs-two .faq-page__accordion--faqs-two .active .accrodion-content p {
  color: var(--wallox-white, #fff);
}
.faqs-two .faq-page__accordion--faqs-two .active .accrodion-title__icon::after {
  background-color: var(--wallox-base, #DF9E42);
}
.faqs-two .sec-title {
  padding-bottom: 20px;
}
.faqs-two__top__text {
  margin-bottom: 20px;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 175%;
  text-transform: capitalize;
  color: var(--wallox-text, #7E7C76);
}
.faqs-two__list {
  margin-bottom: 0;
}
.faqs-two__list__item {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 175%;
  margin-bottom: 0;
  padding-bottom: 0;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
  text-shadow: 0 0 0.1px currentColor;
}
.faqs-two__list__item i {
  color: var(--wallox-base, #DF9E42);
  font-size: 14px;
  margin-right: 10px;
}
.faqs-two__list__item + .faqs-two__list__item {
  margin-top: 10px;
}
.faqs-two__thumb img {
  object-fit: cover;
  width: 100%;
  border-radius: 51px;
}
.faqs-two__element {
  position: absolute;
  top: 45%;
  right: 0;
  animation: topToBottom 3s ease-in-out infinite;
}
@media (max-width: 991px) {
  .faqs-two__element {
    display: none;
  }
}
.faqs-two__element-two {
  position: absolute;
  top: 30%;
  left: 0;
  animation: topToBottom 3s ease-in-out infinite;
  animation-delay: 1s;
}
@media (max-width: 991px) {
  .faqs-two__element-two {
    display: none;
  }
}

/*--------------------------------------------------------------
# Offer
--------------------------------------------------------------*/
.offer-one {
  padding-bottom: 120px;
}
@media (max-width: 767px) {
  .offer-one {
    padding-bottom: 80px;
  }
}
.offer-one--home {
  padding: 100px 0;
}
@media (max-width: 767px) {
  .offer-one--home {
    padding: 80px 0;
  }
}
.offer-one .container-fluid {
  max-width: 1604px;
}
.offer-one__card {
  padding: 20px;
  background-size: cover;
  position: relative;
  overflow: hidden;
}
.offer-one__card::before {
  background: linear-gradient(90deg, rgba(var(--wallox-white-rgb, 255, 255, 255), 0.13) 0px, rgba(var(--wallox-white-rgb, 255, 255, 255), 0.13) 77%, rgba(var(--wallox-white-rgb, 255, 255, 255), 0.5) 92%, rgba(var(--wallox-white-rgb, 255, 255, 255), 0));
  content: "";
  height: 200%;
  left: -210%;
  opacity: 0;
  position: absolute;
  top: -50%;
  transition: all 0.7s ease 0s;
  width: 200%;
}
.offer-one__card:hover::before {
  left: -30%;
  opacity: 1;
  top: -20%;
  transition-duration: 0.7s, 0.7s, 0.15s;
  transition-property: left, top, opacity;
  transition-timing-function: linear;
}
.offer-one__card__inner {
  position: relative;
  border: 1px solid var(--wallox-white, #fff);
  padding: 40px;
}
@media (min-width: 992px) {
  .offer-one__card__inner {
    padding: 50px;
  }
}
.offer-one__card__shape {
  position: absolute;
  top: 0;
  left: 0;
  display: none;
}
@media (min-width: 992px) {
  .offer-one__card__shape {
    display: block;
  }
}
.offer-one__card__value {
  font-family: var(--wallox-special-font, "Outfit", sans-serif);
  font-weight: 400;
  font-size: 30px;
  color: var(--wallox-base, #DF9E42);
  line-height: 1;
  margin: 0;
}
@media (min-width: 992px) {
  .offer-one__card__value {
    font-size: 40px;
  }
}
.offer-one__card__title {
  position: relative;
  font-weight: bold;
  text-transform: uppercase;
  margin: 0;
  font-size: 30px;
  line-height: 1.2em;
  margin-bottom: 17px;
}
@media (min-width: 992px) {
  .offer-one__card__title {
    font-size: 40px;
  }
}

/*--------------------------------------------------------------
# Membership
--------------------------------------------------------------*/
.membership-one {
  padding: 120px 0;
  padding-top: 100px;
}
@media (max-width: 767px) {
  .membership-one {
    padding: 80px 0;
    padding-top: 60px;
  }
}
.membership-one .sec-title {
  text-align: center;
  padding-bottom: 25px;
}
.membership-one__tab__list {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40px;
  flex-wrap: wrap;
}
.membership-one__tab__list li {
  cursor: pointer;
}
.membership-one__tab__list li span {
  display: block;
  font-size: 10px;
  background-color: var(--wallox-gray, #F4EDE4);
  transition: all 500ms ease;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: var(--wallox-letter-space, 0.1em);
  padding: 15px 20px;
  line-height: 1.2em;
  color: var(--wallox-text, #7E7C76);
}
.membership-one__tab__list li.active-btn span, .membership-one__tab__list li:hover span {
  background-color: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
}
.membership-one__card {
  background-repeat: no-repeat;
  background-position: top right;
  border: 1px solid var(--wallox-border-color, #E4DACC);
  padding: 50px;
  background-color: var(--wallox-white, #fff);
  transition: all 500ms ease;
}
.membership-one__card:hover {
  border-color: var(--wallox-white, #fff);
  box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.07);
}
.membership-one__card__price {
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  color: var(--wallox-text-dark, #2E2A20);
  line-height: 1;
  font-size: 40px;
  line-height: 1em;
  font-weight: 500;
}
.membership-one__card__tagline {
  margin: 0;
  font-size: 16px;
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  line-height: 2.5em;
  color: var(--wallox-text, #7E7C76);
  border-bottom: 1px solid var(--wallox-border-color, #E4DACC);
  padding-bottom: 16px;
  margin-bottom: 33px;
}
.membership-one__card__text {
  margin: 0;
  font-weight: 600;
  color: var(--wallox-text-dark, #2E2A20);
  font-size: 16px;
  line-height: 2.5em;
  margin-top: 13px;
}
.membership-one__card__list {
  margin-bottom: 26px;
}
.membership-one__card__list li {
  position: relative;
  font-size: 16px;
  line-height: 2.5em;
  color: var(--wallox-text, #7E7C76);
  display: flex;
  justify-content: center;
  align-items: center;
}
.membership-one__card__list li > i {
  font-size: 14px;
  color: var(--wallox-base, #DF9E42);
  margin-right: 10px;
  position: relative;
  top: 1px;
}

.membership-two {
  padding: 120px 0;
  padding-top: 100px;
}
@media (max-width: 767px) {
  .membership-two {
    padding: 80px 0;
    padding-top: 60px;
  }
}
.membership-two--padding {
  padding: 120px 0;
}
@media (max-width: 767px) {
  .membership-two--padding {
    padding: 80px 0;
  }
}
.membership-two .sec-title {
  text-align: center;
}
.membership-two-card {
  background-repeat: no-repeat;
  background-position: top right;
  border: 1px solid var(--wallox-border-color, #E4DACC);
  padding: 36px 40px;
  transition: all 500ms ease;
  background-color: var(--wallox-white, #fff);
}
.membership-two-card:hover {
  box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.07);
}
@media (min-width: 992px) {
  .membership-two-card {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }
}
.membership-two-card__icon i,
.membership-two-card__icon span {
  color: var(--wallox-base, #DF9E42);
  font-size: 60px;
}
@media (min-width: 992px) {
  .membership-two-card__icon {
    margin-right: 30px;
  }
}
.membership-two-card__title {
  margin: 0;
  text-transform: uppercase;
  color: var(--wallox-text-dark, #2E2A20);
  text-transform: uppercase;
  font-size: 20px;
  line-height: 1.5em;
  font-weight: bold;
}
.membership-two-card__title a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.membership-two-card__title a:hover {
  background-size: 100% 1px;
}
.membership-two-card__text {
  font-size: 15px;
  line-height: 2em;
  margin: 0;
}
.membership-two-card__price {
  margin: 0;
  color: var(--wallox-base, #DF9E42);
  font-size: 16px;
  line-height: 1.6666666667em;
  margin-top: 10px;
}
@media (min-width: 768px) {
  .membership-two-card__price {
    font-size: 18px;
  }
}
@media (min-width: 992px) {
  .membership-two-card__price {
    margin-left: 90px;
  }
}
@media (min-width: 1200px) {
  .membership-two-card__price {
    margin-top: 0;
    margin-left: auto;
  }
}

/*--------------------------------------------------------------
# Gift Card
--------------------------------------------------------------*/
.gift-page {
  padding: 120px 0;
  padding-top: 100px;
}
@media (max-width: 767px) {
  .gift-page {
    padding: 80px 0;
    padding-top: 60px;
  }
}
@media (min-width: 992px) {
  .gift-page__carousel .owl-nav {
    display: none;
  }
}

.gift-card-one {
  background-repeat: no-repeat;
  background-position: left top;
  border: 1px solid var(--wallox-border-color, #E4DACC);
  text-align: center;
  padding: 30px;
  position: relative;
  transition: all 500ms ease;
}
.gift-card-one:hover {
  box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.07);
}
.gift-card-one__inner {
  padding: 45px;
  border: 6px solid var(--wallox-text-dark, #2E2A20);
  transition: all 500ms ease;
}
.gift-card-one:hover .gift-card-one__inner {
  border-color: var(--wallox-base, #DF9E42);
}
.gift-card-one__flower {
  position: absolute;
  top: 0;
  right: 0;
  width: auto !important;
  animation: flowerRotate 2s linear 0s infinite;
}
.gift-card-one__title {
  margin: 0;
  text-transform: uppercase;
  color: var(--wallox-text-dark, #2E2A20);
  font-weight: bold;
  font-size: 25px;
  line-height: 1em;
  margin-top: -5px;
}
@media (min-width: 768px) {
  .gift-card-one__title {
    font-size: 30px;
  }
}
.gift-card-one__title a {
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
  color: inherit;
}
.gift-card-one__title a:hover {
  background-size: 100% 1px;
}
.gift-card-one__title a:hover {
  color: var(--wallox-base, #DF9E42);
}
.gift-card-one__price {
  margin: 0;
  font-size: 20px;
  color: var(--wallox-base, #DF9E42);
  line-height: 1.5em;
}
.gift-card-one__code {
  margin: 0;
  font-size: 16px;
  color: var(--wallox-text-dark, #2E2A20);
  line-height: 22px;
}
.gift-card-one__shape {
  display: block;
  margin-left: auto;
  margin-right: auto;
  width: auto !important;
  margin-top: 25px;
  margin-bottom: 25px;
}
.gift-card-one__text {
  margin: 0;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: var(--wallox-letter-space-xl, 0.2em);
  line-height: 22px;
  margin-bottom: 23px;
}
.gift-card-one__link {
  font-size: 10px;
  padding: 11px 29.5px;
}

/*--------------------------------------------------------------
# Animations
--------------------------------------------------------------*/
@keyframes bubbleMover {
  0% {
    -webkit-transform: translateY(0px) translateX(0) rotate(0);
    transform: translateY(0px) translateX(0) rotate(0);
  }
  30% {
    -webkit-transform: translateY(30px) translateX(50px) rotate(15deg);
    transform: translateY(30px) translateX(50px) rotate(15deg);
    -webkit-transform-origin: center center;
    transform-origin: center center;
  }
  50% {
    -webkit-transform: translateY(50px) translateX(100px) rotate(45deg);
    transform: translateY(50px) translateX(100px) rotate(45deg);
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
  }
  80% {
    -webkit-transform: translateY(30px) translateX(50px) rotate(15deg);
    transform: translateY(30px) translateX(50px) rotate(15deg);
    -webkit-transform-origin: left top;
    transform-origin: left top;
  }
  100% {
    -webkit-transform: translateY(0px) translateX(0) rotate(0);
    transform: translateY(0px) translateX(0) rotate(0);
    -webkit-transform-origin: center center;
    transform-origin: center center;
  }
}
@keyframes border-zooming {
  100% {
    transform: scale(1);
    opacity: 0;
  }
}
@keyframes rotated {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes shapeMover {
  0%, 100% {
    transform: perspective(400px) translateY(0) rotate(0deg) translateZ(0px) translateX(0);
  }
  50% {
    transform: perspective(400px) rotate(-45deg) translateZ(20px) translateY(20px) translateX(20px);
  }
}
@keyframes bounceIn {
  0%, 20%, 40%, 60%, 80%, 100% {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  20% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1);
  }
  40% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(1.03, 1.03, 1.03);
    transform: scale3d(1.03, 1.03, 1.03);
  }
  80% {
    -webkit-transform: scale3d(0.97, 0.97, 0.97);
    transform: scale3d(0.97, 0.97, 0.97);
  }
  100% {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@keyframes textScrolling {
  0% {
    transform: translate3d(-100%, 0, 0);
  }
  100% {
    transform: translate3d(0%, 0, 0);
  }
}
@keyframes banner3Shake {
  0% {
    -webkit-transform: rotate3d(0, 1, 0, 0deg);
    transform: rotate3d(0, 1, 0, 0deg);
  }
  30% {
    -webkit-transform: rotate3d(0, 0, 1, 5deg);
    transform: rotate3d(0, 0, 1, 5deg);
  }
  60% {
    -webkit-transform: rotate3d(1, 0, 0, 0deg);
    transform: rotate3d(1, 0, 0, 0deg);
  }
  80% {
    -webkit-transform: rotate3d(0, 0, 1, 5deg);
    transform: rotate3d(0, 0, 1, 5deg);
  }
  100% {
    -webkit-transform: rotate3d(0, 1, 0, 0deg);
    transform: rotate3d(0, 1, 0, 0deg);
  }
}
@keyframes squareMover {
  0%, 100% {
    -webkit-transform: translate(0, 0) rotate(0);
    transform: translate(0, 0) rotate(0);
  }
  20%, 60% {
    -webkit-transform: translate(20px, 40px) rotate(180deg);
    transform: translate(20px, 40px) rotate(180deg);
  }
  30%, 80% {
    -webkit-transform: translate(40px, 60px) rotate(0deg);
    transform: translate(40px, 60px) rotate(0deg);
  }
}
@keyframes messageMove {
  0%, 100% {
    transform: translateX(0);
  }
  25%, 75% {
    transform: translateX(5px);
  }
  50% {
    transform: translateX(10px);
  }
}
@keyframes textRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes topAniLong {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(10px);
  }
  100% {
    transform: translateY(0px);
  }
}
@keyframes svgRotate {
  8% {
    -webkit-transform: perspective(500px) rotateX(0deg);
    transform: perspective(500px) rotateX(0deg);
  }
  100% {
    -webkit-transform: perspective(500px) rotateX(360deg);
    transform: perspective(500px) rotateX(360deg);
  }
}
@keyframes scale2 {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes top-bottom {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(6px);
  }
  100% {
    transform: translateY(0px);
  }
}
@keyframes topToBottom {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(16px);
  }
  100% {
    transform: translateY(0px);
  }
}
@-webkit-keyframes backInUp {
  0% {
    -webkit-transform: translateY(1200px) scale(0.7);
    transform: translateY(1200px) scale(0.7);
    opacity: 0.7;
  }
  80% {
    -webkit-transform: translateY(0px) scale(0.7);
    transform: translateY(0px) scale(0.7);
    opacity: 0.7;
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
  }
}
@keyframes backInUp {
  0% {
    -webkit-transform: translateY(1200px) scale(0.5);
    transform: translateY(1200px) scale(0.5);
    opacity: 0.7;
  }
  80% {
    -webkit-transform: translateY(0px) scale(0.5);
    transform: translateY(0px) scale(0.5);
    opacity: 0.7;
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
  }
}
.real-image {
  visibility: hidden;
  position: relative;
  width: auto;
  height: 100%;
  overflow: hidden;
}
.real-image img {
  height: auto;
  width: 100%;
  object-fit: cover;
  transform-origin: bottom;
}

.real-image--right {
  visibility: hidden;
  position: relative;
  width: auto;
  height: 100%;
  overflow: hidden;
}
.real-image--right img {
  height: 100%;
  width: 100%;
  object-fit: cover;
  transform-origin: right;
}

/*--------------------------------------------------------------
# Mobile Nav
--------------------------------------------------------------*/
.mobile-nav__wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 999;
  transform: translateX(-100%);
  transform-origin: left center;
  transition: transform 500ms ease 500ms, visibility 500ms ease 500ms;
  visibility: hidden;
  position: fixed;
}
.mobile-nav__wrapper .container {
  padding-left: 0;
  padding-right: 0;
}
.mobile-nav__wrapper .home-showcase .row [class*=col-] {
  flex: 0 0 100%;
}
.mobile-nav__wrapper .home-showcase {
  margin-bottom: -1px;
  margin-top: 0;
  border-bottom: 1px solid RGBA(var(--wallox-white-rgb, 255, 255, 255), 0.1);
}
.mobile-nav__wrapper .home-showcase__inner {
  padding: 15px 0px;
  background-color: transparent;
  box-shadow: none;
}
.mobile-nav__wrapper .home-showcase__title {
  color: var(--wallox-white, #fff);
}

.mobile-nav__wrapper.expanded {
  opacity: 1;
  transform: translateX(0%);
  visibility: visible;
  transition: transform 500ms ease 0ms, visibility 500ms ease 0ms;
}
.mobile-nav__wrapper.expanded .mobile-nav__content {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
  transition: opacity 500ms ease 500ms, visibility 500ms ease 500ms, transform 500ms ease 500ms;
}

.mobile-nav__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--wallox-text-dark, #2E2A20);
  opacity: 0.3;
  cursor: url(../images/close.png), auto;
}

.mobile-nav__content {
  width: 300px;
  background-color: var(--wallox-text-dark, #2E2A20);
  z-index: 10;
  position: relative;
  height: 100%;
  overflow-y: auto;
  padding-top: 30px;
  padding-bottom: 30px;
  padding-left: 15px;
  padding-right: 15px;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-100%);
  transition: opacity 500ms ease 0ms, visibility 500ms ease 0ms, transform 500ms ease 0ms;
}
.mobile-nav__content .main-menu__nav {
  display: block;
  padding: 0;
}

.mobile-nav__content .logo-box {
  margin-bottom: 40px;
  display: flex;
}

.mobile-nav__close {
  position: absolute;
  top: 20px;
  right: 15px;
  font-size: 18px;
  color: var(--wallox-white, #fff);
  cursor: pointer;
}
.mobile-nav__close:hover {
  color: var(--wallox-base, #DF9E42);
}

.mobile-nav__content .main-menu__list,
.mobile-nav__content .main-menu__list ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

.mobile-nav__content .main-menu__list ul {
  display: none;
  border-top: 1px solid RGBA(var(--wallox-white-rgb, 255, 255, 255), 0.1);
}

.mobile-nav__content .main-menu__list ul li > a {
  padding-left: 1em;
}

.mobile-nav__content .main-menu__list li:not(:last-child) {
  border-bottom: 1px solid RGBA(var(--wallox-white-rgb, 255, 255, 255), 0.1);
}

.mobile-nav__content .main-menu__list li > a {
  display: flex;
  justify-content: space-between;
  line-height: 30px;
  color: var(--wallox-white, #fff);
  font-size: 15px;
  font-family: var(--wallox-font, "Plus Jakarta Sans", sans-serif);
  text-transform: capitalize;
  font-weight: 600;
  height: 46px;
  align-items: center;
  transition: 500ms;
}

.mobile-nav__content .main-menu__list li a.expanded {
  color: var(--wallox-base, #DF9E42);
}

.mobile-nav__content .main-menu__list li a button {
  width: 30px;
  height: 30px;
  background-color: var(--wallox-base, #DF9E42);
  border: none;
  outline: none;
  color: var(--wallox-white, #fff);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  transform: rotate(-90deg);
  transition: transform 500ms ease;
}

.mobile-nav__content .main-menu__list li a button.expanded {
  transform: rotate(0deg);
  background-color: var(--wallox-white, #fff);
  color: var(--wallox-text-dark, #2E2A20);
}

.mobile-nav__social {
  display: flex;
  align-items: center;
}
.mobile-nav__social a {
  font-size: 16px;
  color: var(--wallox-white, #fff);
  transition: 500ms;
}
.mobile-nav__social a + a {
  margin-left: 20px;
}
.mobile-nav__social a:hover {
  color: var(--wallox-base, #DF9E42);
}

.mobile-nav__contact {
  margin-bottom: 0;
  margin-top: 20px;
  margin-bottom: 20px;
}
.mobile-nav__contact li {
  color: var(--wallox-white, #fff);
  font-size: 14px;
  font-weight: 500;
  position: relative;
  display: flex;
  align-items: center;
}
.mobile-nav__contact li + li {
  margin-top: 15px;
}
.mobile-nav__contact li a {
  color: inherit;
  transition: 500ms;
}
.mobile-nav__contact li a:hover {
  color: var(--wallox-base, #DF9E42);
}
.mobile-nav__contact li > i {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: var(--wallox-base, #DF9E42);
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  font-size: 12px;
  margin-right: 10px;
  color: var(--wallox-white, #fff);
}

.mobile-nav__container .main-menu__logo,
.mobile-nav__container .main-menu__right {
  display: none;
}

/*--------------------------------------------------------------
# Search Popup
--------------------------------------------------------------*/
.search-popup {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -2;
  -webkit-transition: all 1s ease;
  -khtml-transition: all 1s ease;
  -moz-transition: all 1s ease;
  -ms-transition: all 1s ease;
  -o-transition: all 1s ease;
  transition: all 1s ease;
}
.search-popup__overlay {
  position: fixed;
  width: 224vw;
  height: 224vw;
  top: calc(90px - 112vw);
  right: calc(50% - 112vw);
  z-index: 3;
  display: block;
  -webkit-border-radius: 50%;
  -khtml-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  border-radius: 50%;
  -webkit-transform: scale(0);
  -khtml-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
  -webkit-transform-origin: center;
  transform-origin: center;
  -webkit-transition: transform 0.8s ease-in-out;
  -khtml-transition: transform 0.8s ease-in-out;
  -moz-transition: transform 0.8s ease-in-out;
  -ms-transition: transform 0.8s ease-in-out;
  -o-transition: transform 0.8s ease-in-out;
  transition: transform 0.8s ease-in-out;
  transition-delay: 0s;
  transition-delay: 0.3s;
  -webkit-transition-delay: 0.3s;
  background-color: #000;
  opacity: 0.9;
  cursor: url(../images/close.png), auto;
}
@media (max-width: 767px) {
  .search-popup__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    transform: none;
    width: 100%;
    height: 100%;
    border-radius: 0;
    transform: translateY(-110%);
  }
}
.search-popup__content {
  position: fixed;
  width: 0;
  border-radius: 10px;
  max-width: 560px;
  padding: 30px 15px;
  left: 50%;
  top: 50%;
  opacity: 0;
  z-index: 3;
  -webkit-transform: translate(-50%, -50%);
  -khtml-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-transition: opacity 0.5s 0s, width 0.8s 0.8s cubic-bezier(0.225, 0.01, 0.475, 1.01), transform 0.2s 0s;
  -khtml-transition: opacity 0.5s 0s, width 0.8s 0.8s cubic-bezier(0.225, 0.01, 0.475, 1.01), transform 0.2s 0s;
  -moz-transition: opacity 0.5s 0s, width 0.8s 0.8s cubic-bezier(0.225, 0.01, 0.475, 1.01), transform 0.2s 0s;
  -ms-transition: opacity 0.5s 0s, width 0.8s 0.8s cubic-bezier(0.225, 0.01, 0.475, 1.01), transform 0.2s 0s;
  -o-transition: opacity 0.5s 0s, width 0.8s 0.8s cubic-bezier(0.225, 0.01, 0.475, 1.01), transform 0.2s 0s;
  transition: opacity 0.5s 0s, width 0.8s 0.8s cubic-bezier(0.225, 0.01, 0.475, 1.01), transform 0.2s 0s;
  transition-delay: 0s, 0.8s, 0s;
  transition-delay: 0s, 0.4s, 0s;
  transition-delay: 0.2s;
  -webkit-transition-delay: 0.2s;
}
.search-popup__form {
  position: relative;
}
.search-popup__form input[type=search],
.search-popup__form input[type=text] {
  width: 100%;
  background-color: var(--wallox-white, #fff);
  font-size: 16px;
  font-weight: 600;
  color: var(--wallox-text, #7E7C76);
  border: none;
  outline: none;
  height: 66px;
  padding-left: 30px;
  border-radius: 10px;
}
.search-popup__form input[type=search]::placeholder,
.search-popup__form input[type=text]::placeholder {
  font-size: 16px;
  color: var(--wallox-text, #7E7C76);
  font-weight: 600;
  text-transform: capitalize;
}
.search-popup__form .wallox-btn {
  padding: 0;
  width: 66px;
  height: 66px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  position: absolute;
  top: 0;
  right: -1px;
  border-radius: 0;
  font-size: 18px;
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
  transform: scale(1);
}
.search-popup__form .wallox-btn i {
  margin: 0;
}
.search-popup__form .wallox-btn::before {
  background-color: var(--wallox-text-dark, #2E2A20);
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
  border-radius: 0;
}
.search-popup__form .wallox-btn:hover {
  transform: scale(1);
}
.search-popup.active {
  z-index: 9999;
}
.search-popup.active .search-popup__overlay {
  top: auto;
  bottom: calc(90px - 112vw);
  -webkit-transform: scale(1);
  -khtml-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
  transition-delay: 0s;
  -webkit-transition-delay: 0s;
  opacity: 0.9;
  -webkit-transition: transform 1.6s cubic-bezier(0.4, 0, 0, 1);
  -khtml-transition: transform 1.6s cubic-bezier(0.4, 0, 0, 1);
  -moz-transition: transform 1.6s cubic-bezier(0.4, 0, 0, 1);
  -ms-transition: transform 1.6s cubic-bezier(0.4, 0, 0, 1);
  -o-transition: transform 1.6s cubic-bezier(0.4, 0, 0, 1);
  transition: transform 1.6s cubic-bezier(0.4, 0, 0, 1);
}
@media (max-width: 767px) {
  .search-popup.active .search-popup__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    transform: none;
    width: 100%;
    height: 100%;
    border-radius: 0;
    transform: translateY(0%);
  }
}
.search-popup.active .search-popup__content {
  width: 100%;
  opacity: 1;
  transition-delay: 0.7s;
  -webkit-transition-delay: 0.7s;
}

/*--------------------------------------------------------------
# Page Header
--------------------------------------------------------------*/
.page-header {
  background-color: var(--wallox-text-dark, #2E2A20);
  position: relative;
  padding-top: 110px;
  padding-bottom: 120px;
}
.page-header__bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-repeat: no-repeat;
  background-size: cover;
  opacity: 0.3;
}
.page-header .container {
  position: relative;
  z-index: 10;
  text-align: center;
}
.page-header__title {
  margin: 0;
  font-style: normal;
  font-weight: 700;
  font-size: 45px;
  line-height: 57px;
  text-transform: capitalize;
  margin-bottom: 20px;
  padding-bottom: 0;
  color: var(--wallox-white, #fff);
}

.wallox-breadcrumb__list {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  margin: 0;
}
.wallox-breadcrumb__list li {
  font-style: normal;
  font-weight: 700;
  font-size: 16px;
  line-height: 100%;
  color: var(--wallox-white, #fff);
  display: flex;
  align-items: center;
  text-transform: capitalize;
}
.wallox-breadcrumb__list li i {
  margin-right: 10px;
  font-size: 14px;
}
.wallox-breadcrumb__list li:not(:last-of-type)::after {
  content: "\e909";
  font-family: "icomoon" !important;
  position: relative;
  top: 1px;
  font-weight: 400;
  font-size: 14px;
  margin-left: 10px;
  margin-right: 10px;
}
.wallox-breadcrumb__list li span {
  color: var(--wallox-base, #DF9E42);
}
.wallox-breadcrumb__list li a {
  color: inherit;
  display: inline-flex;
  line-height: 1em;
}

/*--------------------------------------------------------------
# Google Map
--------------------------------------------------------------*/
.google-map {
  position: relative;
}
.google-map iframe {
  position: relative;
  display: block;
  border: none;
  height: 570px;
  width: 100%;
  mix-blend-mode: luminosity;
}
.google-map__contact {
  overflow: hidden;
  background-color: var(--wallox-gray, #F4EDE4);
}

/*--------------------------------------------------------------
# Client Carousel
--------------------------------------------------------------*/
.client-carousel__inner {
  padding: 20px 30px;
  border-radius: 20px;
  border: 1px solid var(--wallox-border-color, #E4DACC);
}
@media (max-width: 991px) {
  .client-carousel__inner {
    padding: 20px;
  }
}
.client-carousel__inner--two {
  background: var(--wallox-white, #fff);
}
.client-carousel__left {
  flex: 35%;
}
.client-carousel__right {
  flex: 65%;
}
.client-carousel__one__item {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 500ms ease;
  position: relative;
  background-color: transparent;
  border-radius: 50px;
  max-width: 90px;
  width: auto;
  height: 80px;
}
.client-carousel__one__item img {
  transition: all 500ms ease;
  width: auto !important;
  cursor: pointer;
}
.client-carousel__one__item:hover .client-carousel__one__hover-image {
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  right: 0;
  margin: auto;
  display: block;
  visibility: visible;
  opacity: 1;
}
.client-carousel__one__item:hover .client-carousel__one__image {
  visibility: hidden;
  opacity: 0;
  transform: translateY(100%);
}
.client-carousel__one__hover-image {
  transition: all 500ms ease;
  display: block;
  position: absolute;
  z-index: 1;
  top: 10px;
  left: 0;
  right: 0;
  margin: auto;
  visibility: hidden;
  opacity: 0;
  max-width: 90px;
}
.client-carousel__one__image {
  transition: 500ms;
  max-width: 122px;
  width: auto;
  transition: all 500ms ease;
}
.client-carousel__left {
  border-right: 1px solid var(--wallox-border-color, #E4DACC);
}
@media (max-width: 767px) {
  .client-carousel__left {
    border-right: 1px solid transparent;
    margin-bottom: 30px;
  }
}
.client-carousel__text {
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 150%;
  text-transform: capitalize;
  margin-bottom: 0;
  padding-bottom: 0;
}
@media (max-width: 991px) {
  .client-carousel__text {
    font-size: 18px;
  }
}
.client-carousel__text a i {
  color: var(--wallox-base, #DF9E42);
  font-size: 16px;
}

/*--------------------------------------------------------------
# Hero Slider
--------------------------------------------------------------*/
.main-slider-one {
  position: relative;
  overflow: hidden;
}
.main-slider-one__carousel {
  position: relative;
  width: 100%;
  counter-reset: my-sec-counter;
}
.main-slider-one__carousel .owl-dots {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  position: absolute;
  margin: auto;
  left: 33px;
  text-align: left;
  top: 50%;
  transform: translateY(-50%);
  width: auto;
  z-index: 2;
}
@media (max-width: 767px) {
  .main-slider-one__carousel .owl-dots {
    width: 100%;
    justify-content: center;
    flex-direction: row;
    top: auto;
    gap: 10px;
    transform: translateY(0%);
    bottom: 30px;
  }
}
@keyframes bottomTop {
  0% {
    transform: translateY(0%) translateX(20%);
    opacity: 1;
  }
  100% {
    transform: translateY(50px) translateX(20%);
    opacity: 0;
  }
}
@keyframes topBottom {
  0% {
    transform: translateY(0%) translateX(20%);
    opacity: 1;
  }
  100% {
    transform: translateY(-50px) translateX(20%);
    opacity: 0;
  }
}
.main-slider-one__carousel .owl-dots::before {
  content: "";
  width: 16px;
  height: 60px;
  background-image: url(../images/shapes/long-arrow-bottom.png);
  background-repeat: no-repeat;
  background-size: contain;
  background-position: 0 0;
  position: absolute;
  bottom: -60px;
  transform: translateX(20%);
  left: 0;
  animation: bottomTop 3s ease-in-out infinite;
}
@media (max-width: 767px) {
  .main-slider-one__carousel .owl-dots::before {
    display: none;
  }
}
.main-slider-one__carousel .owl-dots::after {
  content: "";
  width: 16px;
  height: 60px;
  background-image: url(../images/shapes/long-arrow-top.png);
  background-repeat: no-repeat;
  background-size: contain;
  background-position: 0 0;
  position: absolute;
  top: -60px;
  transform: translateX(20%);
  left: 0;
  animation: topBottom 3s ease-in-out infinite;
}
@media (max-width: 767px) {
  .main-slider-one__carousel .owl-dots::after {
    display: none;
  }
}
.main-slider-one__carousel .owl-dots .owl-dot {
  display: inline-flex;
  margin: 0 3px;
}
.main-slider-one__carousel .owl-dots .owl-dot span {
  background-color: transparent;
  margin: 0;
  transition: all 300ms ease;
  counter-increment: my-sec-counter;
  position: relative;
  width: auto 0;
}
.main-slider-one__carousel .owl-dots .owl-dot span::before {
  content: counters(my-sec-counter, ".", decimal-leading-zero);
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 18px;
  color: var(--wallox-text, #7E7C76);
}
.main-slider-one__carousel .owl-dots .owl-dot:hover span::before,
.main-slider-one__carousel .owl-dots .owl-dot.active span::before {
  color: var(--wallox-base, #DF9E42);
}
.main-slider-one__item {
  position: relative;
  padding-top: 115px;
  padding-bottom: 158px;
  height: 803px;
  background-color: var(--wallox-gray, #F4EDE4);
}
@media (max-width: 1350px) {
  .main-slider-one__item {
    height: auto;
  }
}
@media (max-width: 767px) {
  .main-slider-one__item {
    padding-top: 180px;
    padding-bottom: 200px;
  }
}
.main-slider-one__bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  overflow: hidden;
  transition: transform 1000ms ease, opacity 1000ms ease;
  transform: translateX(50%) scaleX(2);
  opacity: 0;
}
.main-slider-one__bg::after {
  content: "";
  width: 100%;
  height: 100%;
  background: linear-gradient(99.87deg, #F4EDE4 0%, rgba(244, 237, 228, 0.84) 28.96%, rgba(244, 237, 228, 0) 100.49%);
  position: absolute;
  top: 0;
  left: 0;
}
@media (min-width: 1550px) {
  .main-slider-one .container {
    max-width: 1550px;
    margin-left: auto;
    margin-right: auto;
  }
}
.main-slider-one__content {
  position: relative;
  z-index: 3;
  overflow: hidden;
}
.main-slider-one__sub-title {
  color: var(--wallox-text, #7E7C76);
  font-size: 16px;
  font-weight: 600;
  text-transform: uppercase;
  line-height: 1;
  margin: 0 0 15px;
  padding: 5px 20px;
  border-radius: 100px;
  background-color: var(--wallox-white, #fff);
  display: inline-block;
  opacity: 0;
  transition: transform 1000ms ease, opacity 1000ms ease;
  transform: translateY(-200px);
  letter-spacing: 0.15em;
}
.main-slider-one__sub-title span {
  color: var(--wallox-base, #DF9E42);
}
@media (max-width: 575px) {
  .main-slider-one__sub-title {
    font-size: 14px;
    letter-spacing: 0.01em;
  }
}
.main-slider-one__title {
  margin: 0 0 38px;
}
.main-slider-one__title__box {
  overflow: hidden;
}
.main-slider-one__title__box .main-slider-one__title__text {
  display: block;
  opacity: 0;
  transition: transform 1200ms ease, opacity 1200ms ease;
  transform: translateY(200px);
  color: var(--wallox-text-dark, #2E2A20);
  text-transform: uppercase;
  font-style: normal;
  font-weight: 800;
  font-size: 80px;
  line-height: 112%;
  letter-spacing: -0.02em;
  margin: 0;
  text-transform: capitalize;
}
@media (max-width: 1350px) {
  .main-slider-one__title__box .main-slider-one__title__text {
    font-size: 60px;
  }
}
@media (max-width: 1199px) and (min-width: 992px) {
  .main-slider-one__title__box .main-slider-one__title__text {
    font-size: 50px;
  }
}
@media (max-width: 575px) {
  .main-slider-one__title__box .main-slider-one__title__text {
    font-size: 50px;
  }
}
.main-slider-one__btn {
  position: relative;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  z-index: 5;
  overflow: hidden;
  opacity: 0;
  transform: perspective(400px) rotateY(0deg) translateY(80px);
  transform-origin: bottom;
  transition: all 1500ms ease;
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 4px;
}
.main-slider-one__btn a {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  padding: 15px 25px;
  border: 2px solid transparent;
}
.main-slider-one__btn a:last-child {
  border-color: var(--wallox-base, #DF9E42);
  color: var(--wallox-base, #DF9E42);
}
.main-slider-one__btn a:last-child:hover {
  border-color: transparent;
  color: var(--wallox-white, #fff);
}
.main-slider-one__btn a:last-child:hover::before {
  background-color: var(--wallox-base, #DF9E42);
}
.main-slider-one__btn a:hover {
  border-color: var(--wallox-base, #DF9E42);
  color: var(--wallox-base, #DF9E42);
}
.main-slider-one__btn a:hover::before {
  background-color: transparent;
}
.main-slider-one__thumb {
  position: relative;
  z-index: 1;
}
.main-slider-one__thumb__item {
  opacity: 0;
  transition: transform 1000ms ease, opacity 1000ms ease;
  transform: translateY(-200px);
}
.main-slider-one__thumb__box__bg {
  max-width: 508px;
  height: 508px;
  width: 100%;
  border-radius: 50%;
  background-color: var(--wallox-base, #DF9E42);
  position: absolute;
  bottom: 40%;
  transform: translateY(50%) scale(0.3);
  right: 0px;
  z-index: -1;
  opacity: 0;
  transition: transform 1000ms ease, opacity 1000ms ease;
}
@media (max-width: 1466px) {
  .main-slider-one__thumb__box__bg {
    right: -80px;
  }
}
@media (max-width: 1199px) {
  .main-slider-one__thumb__box__bg {
    max-width: 380px;
    height: 380px;
    right: -60px;
  }
}
@media (max-width: 450px) {
  .main-slider-one__thumb__box__bg {
    display: none;
  }
}
.main-slider-one__thumb__box__bg::before {
  content: "";
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  position: absolute;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  mix-blend-mode: multiply;
  background-image: url(../images/shapes/hero-1-4.png);
}
.main-slider-one__thumb__box__bg::after {
  position: absolute;
  content: "";
  border-radius: 50%;
  top: -22px;
  left: -22px;
  bottom: -22px;
  right: -22px;
  border-width: 2px;
  border-style: solid;
  border-color: var(--wallox-base, #DF9E42);
}
.main-slider-one__element-one {
  position: absolute;
  top: 10%;
  left: 2%;
  z-index: 1;
  transition: transform 1000ms ease, opacity 1000ms ease;
  transform: translateX(700px);
  opacity: 0;
}
.main-slider-one__element-one .main-slider-one__element__item {
  animation: topToBottom 3.5s ease-in-out infinite;
}
.main-slider-one__element-two {
  position: absolute;
  bottom: 10%;
  right: 45%;
  z-index: 1;
  transition: transform 1000ms ease, opacity 1000ms ease;
  transform: translateX(-700px);
  opacity: 0;
}
.main-slider-one__element-two .main-slider-one__element__item {
  animation: topToBottom 3s ease-in-out infinite;
}
.main-slider-one__element-three {
  position: absolute;
  top: 10%;
  left: 50%;
  z-index: 1;
  transition: transform 1000ms ease, opacity 1000ms ease;
  transform: translateY(-800px);
  opacity: 0;
}
.main-slider-one__element-three .main-slider-one__element__item {
  animation: scale2 3s ease-in-out infinite;
}
.main-slider-one .active .main-slider-one__bg {
  opacity: 1;
  transform: translateX(0) scaleX(1);
  filter: blur(0);
  transition-delay: 1000ms;
}
.main-slider-one .active .main-slider-one__sub-title {
  opacity: 1;
  transform: translateX(0);
  transition-delay: 1300ms;
}
.main-slider-one .active .main-slider-one__title__box h2 {
  opacity: 1;
  transform: translateX(0);
  transition-delay: 1500ms;
}
.main-slider-one .active .main-slider-one__btn {
  opacity: 1;
  transform: perspective(400px) rotateY(0deg) translateY(0px);
  transition-delay: 1700ms;
}
.main-slider-one .active .main-slider-one__element-one {
  transform: translateX(0px);
  opacity: 1;
  transition-delay: 1500ms;
}
.main-slider-one .active .main-slider-one__element-two {
  transform: translateX(0px);
  opacity: 1;
  transition-delay: 1500ms;
}
.main-slider-one .active .main-slider-one__element-three {
  transform: translateY(0px);
  opacity: 1;
  transition-delay: 1500ms;
}
.main-slider-one .active .main-slider-one__thumb__box__bg {
  transform: translateY(50%) scale(1);
  opacity: 1;
  transition-delay: 1100ms;
}
.main-slider-one .active .main-slider-one__thumb__item {
  opacity: 1;
  transform: translateX(0);
  transition-delay: 1200ms;
}

.hero-slider-two {
  position: relative;
}
.hero-slider-two__carousel {
  position: relative;
  width: 100%;
}
.hero-slider-two__carousel .owl-nav {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-direction: column;
  position: absolute;
  top: 50%;
  left: 30px;
  transform: translateY(-50%);
}
@media (max-width: 991px) {
  .hero-slider-two__carousel .owl-nav {
    top: auto;
    bottom: 60px;
    left: 50%;
    flex-direction: row;
    transform: translateY(0) translateX(-50%);
  }
}
.hero-slider-two__carousel .owl-nav button {
  border: none;
  outline: none;
  border-radius: 50%;
  margin: 0;
  padding: 0;
}
.hero-slider-two__carousel .owl-nav button span {
  border: none;
  outline: none;
  width: 54px;
  height: 54px;
  background-color: var(--wallox-white, #fff);
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--wallox-text, #7E7C76);
  border-radius: 50%;
  font-size: 16px;
  transition: all 500ms ease;
}
.hero-slider-two__carousel .owl-nav button span:hover {
  background-color: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
}
@media (max-width: 991px) {
  .hero-slider-two__carousel .owl-nav .owl-prev span {
    transform: rotate(265deg);
  }
}
@media (max-width: 991px) {
  .hero-slider-two__carousel .owl-nav .owl-next span {
    transform: rotate(265deg);
  }
}
.hero-slider-two__social {
  background-color: var(--wallox-white, #fff);
  padding: 15px;
  border-radius: 100px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  position: absolute;
  z-index: 3;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 55px;
}
@media (max-width: 1200px) {
  .hero-slider-two__social {
    display: none;
  }
}
.hero-slider-two__social a {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  border: 1px solid var(--wallox-border-color, #E4DACC);
  color: var(--wallox-text, #7E7C76);
  position: relative;
  z-index: 1;
}
.hero-slider-two__social a:hover {
  color: var(--wallox-white, #fff);
  border-color: var(--wallox-base, #DF9E42);
  background-color: var(--wallox-base, #DF9E42);
}
.hero-slider-two__item {
  position: relative;
  z-index: 1;
  padding-top: 83px;
  padding-bottom: 83px;
  height: 885px;
}
@media (max-width: 1600px) {
  .hero-slider-two__item {
    height: auto;
  }
}
@media (max-width: 991px) {
  .hero-slider-two__item {
    padding-bottom: 200px;
  }
}
@media (max-width: 676px) {
  .hero-slider-two__item {
    padding-bottom: 100px;
  }
}
.hero-slider-two__bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  overflow: hidden;
  transition: transform 1000ms ease, opacity 1000ms ease;
  transform: translateX(50%) scaleX(2);
  opacity: 0;
}
.hero-slider-two__bg::after {
  content: "";
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(244, 237, 228, 0.9) 0%, rgba(244, 237, 228, 0.8) 100%);
  position: absolute;
  top: 0;
  left: 0;
}
.hero-slider-two__content {
  position: relative;
  z-index: 3;
}
.hero-slider-two__content__border {
  position: absolute;
  top: 15px;
  left: -32px;
  opacity: 0;
  transition: opacity 1000ms ease;
}
@media (max-width: 991px) {
  .hero-slider-two__content__border {
    display: none;
  }
}
@media (min-width: 1600px) {
  .hero-slider-two .container {
    max-width: 1630px;
    margin-left: auto;
    margin-right: auto;
  }
}
.hero-slider-two__sub-title {
  margin: 0 0 16px 55px;
  transition: transform 1000ms ease, opacity 1000ms ease;
  transform: translateY(-200px);
  opacity: 0;
  text-transform: uppercase;
  font-style: normal;
  font-weight: 700;
  font-size: 15px;
  line-height: 19px;
  color: var(--wallox-base, #DF9E42);
  padding: 5px 20px;
  display: inline-block;
  border-radius: 100px;
  border: 1px solid var(--wallox-base, #DF9E42);
}
@media (max-width: 991px) {
  .hero-slider-two__sub-title {
    margin: 0 0 16px 0px;
  }
}
.hero-slider-two__title {
  margin: 0 0 60px;
}
@media (max-width: 991px) {
  .hero-slider-two__title {
    margin: 0 0 40px;
  }
}
.hero-slider-two__title__box {
  position: relative;
}
.hero-slider-two__title__box:nth-child(1) .hero-slider-two__title__text, .hero-slider-two__title__box:nth-child(4) .hero-slider-two__title__text {
  overflow: hidden;
  display: block;
  font-style: normal;
  font-weight: 800;
}
.hero-slider-two__title__box:nth-child(2) .hero-slider-two__title__text {
  overflow: hidden;
  display: block;
  font-style: normal;
  font-weight: 500;
}
.hero-slider-two__title__box:nth-child(3) .hero-slider-two__title__text {
  color: transparent;
  -webkit-text-stroke: 1.5px var(--wallox-text-dark, #2E2A20);
  position: relative;
}
.hero-slider-two__title__box .hero-slider-two__title__text {
  opacity: 0;
  transition: transform 1200ms ease, opacity 1200ms ease;
  transform: translateY(200px);
  display: block;
  display: inline-block;
  color: var(--wallox-text-dark, #2E2A20);
  font-style: normal;
  font-weight: 800;
  font-size: 80px;
  line-height: 106%;
  letter-spacing: -0.02em;
  text-transform: capitalize;
  position: relative;
  margin: 0;
}
@media (max-width: 1600px) {
  .hero-slider-two__title__box .hero-slider-two__title__text {
    font-size: 60px;
  }
}
@media (max-width: 575px) {
  .hero-slider-two__title__box .hero-slider-two__title__text {
    font-size: 50px;
  }
}
@keyframes opesityIn {
  0% {
    opacity: 0;
    width: 20px;
  }
  80% {
    opacity: 1;
    width: 100%;
  }
  100% {
    opacity: 0;
    width: 20px;
  }
}
.hero-slider-two__title__element {
  position: absolute;
  right: 10%;
  z-index: 2;
  bottom: -50%;
  opacity: 0;
  transition: transform 1200ms ease, opacity 1200ms ease;
  transform: translateY(200px);
}
.hero-slider-two__title__element img {
  animation: opesityIn 4s ease-in-out infinite;
}
@media (max-width: 991px) {
  .hero-slider-two__title__element {
    right: 0%;
  }
}
@media (max-width: 575px) {
  .hero-slider-two__title__element {
    display: none;
  }
}
.hero-slider-two__btn {
  position: relative;
  display: flex;
  justify-content: start;
  align-items: center;
  gap: 20px;
  z-index: 5;
  overflow: hidden;
  padding: 4px;
  opacity: 0;
  transform: perspective(400px) rotateY(0deg) translateY(80px);
  transform-origin: bottom;
  transition: all 1500ms ease;
}
.hero-slider-two__btn .wallox-btn:last-child {
  border-color: var(--wallox-text-dark, #2E2A20);
  color: var(--wallox-text-dark, #2E2A20);
}
.hero-slider-two__btn .wallox-btn:last-child:hover {
  color: var(--wallox-white, #fff);
  border-color: transparent;
}
.hero-slider-two__thumb {
  display: flex;
  align-items: center;
  gap: 30px;
  position: relative;
  margin-left: -40px;
  z-index: 1;
}
.hero-slider-two__thumb__item {
  border-radius: 235px;
  background: linear-gradient(99.87deg, #F4EDE4 0%, rgba(244, 237, 228, 0.84) 28.96%, rgba(244, 237, 228, 0) 100.49%);
  padding: 10px;
  opacity: 0;
  transition: transform 1000ms ease, opacity 1000ms ease;
  transform: translateY(-200px);
}
@media (max-width: 575px) {
  .hero-slider-two__thumb__item {
    display: none;
  }
}
.hero-slider-two__thumb__item img {
  border-radius: 235px;
  object-fit: contain;
  width: 100%;
}
.hero-slider-two__thumb__item--two {
  opacity: 0;
  transition: transform 1200ms ease, opacity 1200ms ease;
  transform: translateY(-200px);
}
.hero-slider-two__thumb__element {
  position: absolute;
  width: 989px;
  height: 302px;
  border-radius: 100%;
  background-color: var(--wallox-base, #DF9E42);
  z-index: -1;
  left: 0;
  transform: scale(0.3) rotate(-31.71deg);
  opacity: 0;
  transition: transform 1000ms ease, opacity 1000ms ease;
}
@media (max-width: 1600px) {
  .hero-slider-two__thumb__element {
    width: 689px;
  }
}
@media (max-width: 1199px) {
  .hero-slider-two__thumb__element {
    width: 600px;
  }
}
@media (max-width: 676px) {
  .hero-slider-two__thumb__element {
    display: none;
  }
}
.hero-slider-two__thumb__element::after {
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  mix-blend-mode: multiply;
  background-image: url(../images/shapes/hero-2-1.png);
  opacity: 0.15;
  transform: rotate(0deg);
  border-radius: 100%;
}
.hero-slider-two__thumb__element::before {
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  bottom: -30px;
  left: 0;
  transform: rotate(0deg);
  border-radius: 100%;
  background-color: transparent;
  border: 2px solid var(--wallox-base, #DF9E42);
}
.hero-slider-two .active .hero-slider-two__bg {
  opacity: 1;
  transform: translateX(0) scaleX(1);
  filter: blur(0);
  transition-delay: 1000ms;
}
.hero-slider-two .active .hero-slider-two__sub-title {
  opacity: 1;
  transform: translateX(0);
  transition-delay: 1300ms;
}
.hero-slider-two .active .hero-slider-two__title__box .hero-slider-two__title__text {
  opacity: 1;
  transform: translateX(0);
  transition-delay: 1500ms;
}
.hero-slider-two .active .hero-slider-two__btn {
  opacity: 1;
  transform: perspective(400px) rotateY(0deg) translateY(0px);
  transition-delay: 1700ms;
}
.hero-slider-two .active .hero-slider-two__thumb__item {
  opacity: 1;
  transform: translateY(0px);
  transition-delay: 1500ms;
}
.hero-slider-two .active .hero-slider-two__thumb__item--two {
  opacity: 1;
  transform: translateY(0px);
  transition-delay: 1700ms;
}
.hero-slider-two .active .hero-slider-two__thumb__element {
  transform: scale(1) rotate(-31.71deg);
  opacity: 1;
  transition-delay: 1500ms;
}
.hero-slider-two .active .hero-slider-two__title__element {
  opacity: 1;
  transform: translateY(0px);
  transition-delay: 1500ms;
}
.hero-slider-two .active .hero-slider-two__content__border {
  opacity: 1;
  transition-delay: 1500ms;
}

.hero-three {
  position: relative;
}
.hero-three__carousel {
  position: relative;
  width: 100%;
}
.hero-three__carousel .slick-dots {
  margin: auto 0;
  position: absolute;
  right: 33px;
  text-align: left;
  top: 50%;
  transform: translateY(-10%);
  width: auto;
}
@media (max-width: 1400px) {
  .hero-three__carousel .slick-dots {
    width: auto;
    right: 20px;
  }
}
@media (max-width: 1199px) {
  .hero-three__carousel .slick-dots {
    width: 40px;
  }
}
@media (max-width: 767px) {
  .hero-three__carousel .slick-dots {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    width: auto;
    margin-top: -100px;
    left: 0;
    top: auto;
    bottom: 40px;
  }
}
.hero-three__carousel .slick-dots li {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: transparent;
  border: 2px solid transparent;
  margin: 0;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  position: relative;
}
.hero-three__carousel .slick-dots li::after {
  content: "";
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--wallox-white, #fff);
  transition: all 0.4s ease-in-out;
}
.hero-three__carousel .slick-dots button {
  display: none;
  margin: 2px 0;
}
@media (max-width: 767px) {
  .hero-three__carousel .slick-dots button {
    margin: 0 2px;
  }
}
.hero-three__carousel .slick-dots li:hover,
.hero-three__carousel .slick-dots li.slick-active {
  background-color: transparent;
  border: 2px solid var(--wallox-white, #fff);
}
.hero-three__carousel .slick-dots li:hover::after,
.hero-three__carousel .slick-dots li.slick-active::after {
  background-color: var(--wallox-base, #DF9E42);
}
.hero-three__carousel .slick-dotted.slick-slider {
  margin-bottom: 0 !important;
}
.hero-three__item {
  background-color: var(--wallox-text-dark, #2E2A20);
  position: relative;
  z-index: 3;
  padding-top: 245px;
  padding-bottom: 245px;
  height: 800px;
}
@media (max-width: 991px) {
  .hero-three__item {
    padding-top: 120px;
    padding-bottom: 120px;
    height: auto;
  }
}
@media (max-width: 767px) {
  .hero-three__item {
    padding-top: 100px;
    padding-bottom: 120px;
  }
}
.hero-three__bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 1000ms ease, opacity 1000ms ease;
  transform: translateX(50%) scaleX(2);
  opacity: 0;
  z-index: -1;
}
.hero-three__bg::before {
  position: absolute;
  left: 0;
  top: 0;
  background-color: rgba(var(--wallox-text-dark-rgb, 46, 42, 32), 0.8);
  width: 100%;
  height: 100%;
  content: "";
}
.hero-three__content {
  position: relative;
  z-index: 3;
  overflow: hidden;
}
.hero-three__sub-title {
  display: inline-block;
  color: var(--wallox-base, #DF9E42);
  text-transform: uppercase;
  transition: transform 1000ms ease, opacity 1000ms ease;
  transform: translateY(-200px);
  font-style: normal;
  font-weight: 700;
  font-size: 15px;
  line-height: 19px;
  padding: 4px 10px;
  border-radius: 100px;
  border: 1px solid var(--wallox-base, #DF9E42);
}
.hero-three__title {
  color: var(--wallox-white, #fff);
  overflow: hidden;
  margin: 10px 0 38px;
  opacity: 0;
  transition: transform 1200ms ease, opacity 1200ms ease;
  transform: translateX(200px);
  font-style: normal;
  font-weight: 800;
  font-size: 80px;
  line-height: 112%;
  text-transform: capitalize;
}
@media (max-width: 1199px) {
  .hero-three__title {
    font-size: 70px;
  }
}
@media (max-width: 991px) {
  .hero-three__title {
    font-size: 50px;
  }
}
@media (max-width: 767px) {
  .hero-three__title {
    font-size: 45px;
  }
}
@media (max-width: 575px) {
  .hero-three__title {
    font-size: 40px;
  }
}
.hero-three__title::after {
  content: "";
  width: 101%;
  height: 101%;
  position: absolute;
  top: 0px;
  left: 100%;
  background: currentColor;
  transition: 1s cubic-bezier(0.858, 0.01, 0.068, 0.99);
  z-index: 3;
  transform: translateX(-100%);
  transition-delay: 1s;
}
.hero-three__btn {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  z-index: 5;
  padding-top: 4px;
  padding-bottom: 4px;
  overflow: hidden;
  opacity: 0;
  transform: perspective(400px) rotateY(0deg) translateY(80px);
  transform-origin: bottom;
  transition: all 1500ms ease;
  z-index: 1;
}
@media (max-width: 400px) {
  .hero-three__btn {
    flex-direction: column;
  }
}
.hero-three__btn a {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  padding: 15px 25px;
  border: 1px solid transparent;
}
.hero-three__btn a:last-child {
  border-color: var(--wallox-white, #fff);
}
.hero-three__btn a:last-child:hover {
  border-color: transparent;
}
.hero-three__btn a:last-child:hover::before {
  background-color: var(--wallox-base, #DF9E42);
}
.hero-three__btn a:hover {
  border-color: var(--wallox-white, #fff);
}
.hero-three__btn a:hover::before {
  background-color: transparent;
}
.hero-three .slick-active .hero-three__bg {
  opacity: 1;
  transform: translateX(0) scaleX(1);
  filter: blur(0);
  transition-delay: 1000ms;
}
.hero-three .slick-active .hero-three__sub-title {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 1100ms;
}
.hero-three .slick-active .hero-three__title {
  opacity: 1;
  transform: translateX(0);
  transition-delay: 1300ms;
}
.hero-three .slick-active .hero-three__title::after {
  transform: translateX(1%);
  transition-delay: 1500ms;
}
.hero-three .slick-active .hero-three__btn {
  opacity: 1;
  transform: perspective(400px) rotateY(0deg) translateY(0px);
  transition-delay: 1700ms;
}
.hero-three__counter__carousel {
  width: 100px;
  height: 100px;
  overflow: hidden;
  position: absolute;
  top: 50%;
  transform: rotate(90deg) translate(-50%, 40%);
}
@media (max-width: 767px) {
  .hero-three__counter__carousel {
    display: none;
  }
}
.hero-three__counter__carousel .slick-current.slick-active + .slick-active span {
  color: var(--wallox-base, #DF9E42);
  text-shadow: 0 0 0.3px currentColor;
}
.hero-three__counter__item span {
  transform: rotate(180deg);
  writing-mode: vertical-rl;
  display: block;
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 18px;
  color: var(--wallox-text, #7E7C76);
}

/*--------------------------------------------------------------
# Feature Section
--------------------------------------------------------------*/
/*feature One Style*/
.feature-one {
  padding: 120px 0;
  background-color: var(--wallox-white, #fff);
}
@media (max-width: 100px) {
  .feature-one {
    padding: 100px 0;
  }
}
@media (max-width: 767px) {
  .feature-one {
    padding: 80px 0;
  }
}
.feature-one__item {
  text-align: center;
}
.feature-one__item__inner {
  width: 270px;
  height: 270px;
  border-radius: 50%;
  position: relative;
  background-color: var(--wallox-gray, #F4EDE4);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
  overflow: hidden;
  margin-bottom: 22px;
  transition: all 0.4s ease-in-out;
}
.feature-one__item__inner::after {
  content: "";
  background-image: url(../../assets/images/shapes/feature-bg.png);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  mix-blend-mode: multiply;
  z-index: -1;
}
@media (max-width: 1199px) and (min-width: 576px) {
  .feature-one__item__inner {
    width: 200px;
    height: 200px;
  }
}
@media (max-width: 575px) {
  .feature-one__item__inner {
    margin-left: auto;
    margin-right: auto;
  }
}
.feature-one__item__icon {
  width: 130px;
  height: 130px;
  border-radius: 50%;
  background-color: var(--wallox-white, #fff);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 63px;
  color: var(--wallox-base, #DF9E42);
}
@media (max-width: 1199px) and (min-width: 576px) {
  .feature-one__item__icon {
    width: 90px;
    height: 90px;
    font-size: 40px;
  }
}
.feature-one__item__title {
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 140%;
  margin-bottom: 0;
  padding-bottom: 0;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
}
.feature-one__item__title a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.feature-one__item__title a:hover {
  background-size: 100% 1px;
}
.feature-one__item__title a:hover {
  color: var(--wallox-base, #DF9E42);
}
.feature-one__item:hover .feature-one__item__inner {
  background-color: var(--wallox-base, #DF9E42);
}
.feature-one__item:hover .feature-one__item__icon {
  animation: bounceIn 1s linear;
}

/*feature Two Style*/
.feature-two {
  padding: 0px 0 120px;
}
@media (max-width: 100px) {
  .feature-two {
    padding: 0 0 100px;
  }
}
@media (max-width: 767px) {
  .feature-two {
    padding: 0 0 80px;
  }
}
.feature-two--one {
  padding: 120px 0 60px;
}
@media (max-width: 100px) {
  .feature-two--one {
    padding: 100px 0 60px;
  }
}
@media (max-width: 767px) {
  .feature-two--one {
    padding: 80px 0 60px;
  }
}
.feature-two .container {
  position: relative;
}
.feature-two .line {
  width: 100%;
  height: 1px;
  background-color: var(--wallox-border-color, #E4DACC);
  position: absolute;
  bottom: -60px;
}
.feature-two__item {
  background: var(--wallox-gray, #F4EDE4);
  border-radius: 100px;
  overflow: hidden;
  position: relative;
  z-index: 1;
  padding: 19px;
  display: flex;
  align-items: center;
  gap: 20px;
}
@media (max-width: 1199px) and (min-width: 991px) {
  .feature-two__item {
    gap: 10px;
    padding: 14px;
  }
}
.feature-two__item::after {
  content: "";
  background-image: url(../../assets/images/shapes/feature-2-1.png);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  mix-blend-mode: multiply;
  z-index: -1;
  opacity: 0.15;
}
.feature-two__item__icon {
  max-width: 62px;
  width: 100%;
  height: 62px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--wallox-white, #fff);
  font-size: 30px;
  color: var(--wallox-base, #DF9E42);
  transition: all 0.4s ease-in-out;
  position: relative;
  z-index: 1;
}
@media (max-width: 1199px) and (min-width: 991px) {
  .feature-two__item__icon {
    max-width: 50px;
    width: 100%;
    height: 50px;
    font-size: 24px;
  }
}
.feature-two__item__icon::after {
  content: "";
  position: absolute;
  top: 0%;
  height: 0;
  width: 0;
  left: 50%;
  background-color: var(--wallox-text-dark, #2E2A20);
  border-radius: 50%;
  transition: all 0.4s ease;
  z-index: -1;
}
.feature-two__item__title {
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 140%;
  margin-bottom: 0;
  padding-bottom: 0;
  color: var(--wallox-text-dark, #2E2A20);
  text-transform: capitalize;
}
@media (max-width: 1199px) and (min-width: 991px) {
  .feature-two__item__title {
    font-size: 18px;
  }
}
.feature-two__item__title a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.feature-two__item__title a:hover {
  background-size: 100% 1px;
}
.feature-two__item:hover .feature-two__item__icon {
  color: var(--wallox-base, #DF9E42);
}
.feature-two__item:hover .feature-two__item__icon i {
  animation: top-bottom 0.4s ease-in-out;
}
.feature-two__item:hover .feature-two__item__icon::after {
  top: 0%;
  height: 100%;
  width: 100%;
  left: 0%;
}
.feature-two__item:hover {
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
  background-color: var(--wallox-base, #DF9E42);
}
.feature-two__item:hover .feature-two__item__title {
  color: var(--wallox-white, #fff);
}

/*Design One Style*/
.design-one {
  padding-top: 45px;
  padding-bottom: 165px;
  position: relative;
}
@media (max-width: 1400px) {
  .design-one {
    padding-top: 0px;
    padding-bottom: 100px;
  }
}
@media (max-width: 767px) {
  .design-one {
    padding-bottom: 80px;
  }
}
.design-one__thumb {
  position: absolute;
  width: calc(50% - 10px);
  top: 40%;
  transform: translateY(-50%);
  left: 0;
  padding: 60px;
  background: var(--wallox-white, #fff);
  border-radius: 0px 1000px 1000px 0px;
  display: flex;
  align-items: center;
  gap: 30px;
  overflow: hidden;
}
@media (max-width: 1199px) {
  .design-one__thumb {
    position: relative;
    width: 100%;
    transform: translateY(0%);
  }
}
@media (max-width: 1199px) and (min-width: 991px) {
  .design-one__thumb {
    padding: 30px;
  }
}
@media (max-width: 767px) {
  .design-one__thumb {
    padding: 30px;
    flex-direction: column;
    justify-content: start;
    align-items: start;
    border-radius: 30px;
  }
}
.design-one__thumb__item img {
  border-radius: 50%;
  object-fit: cover;
  width: 100%;
}
.design-one__right .sec-title {
  padding-bottom: 20px;
}
.design-one__tab__link {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 25px;
}
.design-one__tab__link .tab-btn {
  border: 0px solid transparent;
  padding: 5px 13.3px;
  background: var(--wallox-white, #fff);
  color: var(--wallox-text-dark, #2E2A20);
  border-radius: 100px;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 175%;
}
.design-one__tab__link .tab-btn:hover {
  color: var(--wallox-white, #fff);
}
.design-one__tab__link .active-btn {
  color: var(--wallox-white, #fff);
  background-color: var(--wallox-base, #DF9E42);
}
.design-one__tab__link .active-btn::before {
  transform: translate(0, 0);
}
.design-one__inner__text {
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 175%;
  text-transform: capitalize;
  color: var(--wallox-text, #7E7C76);
}
.design-one__inner__item {
  margin-bottom: 0;
  margin-left: 0;
}
.design-one__inner__item__text {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 175%;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
  text-shadow: 0 0 0.1px currentColor;
}
.design-one__inner__item__text i {
  color: var(--wallox-base, #DF9E42);
  margin-right: 10px;
  font-size: 15px;
}
.design-one__inner__item__text + .design-one__inner__item__text {
  margin-top: 7px;
}

/*--------------------------------------------------------------
# About
--------------------------------------------------------------*/
.about-one {
  padding: 120px 0px;
  position: relative;
}
@media (max-width: 991px) {
  .about-one {
    padding: 100px 0px;
  }
}
@media (max-width: 767px) {
  .about-one {
    padding: 80px 0px;
  }
}
.about-one__thumb {
  position: relative;
  z-index: 1;
  margin-right: 30px;
}
@media (max-width: 991px) {
  .about-one__thumb {
    margin-right: 0;
  }
}
.about-one__thumb img {
  position: relative;
  object-fit: cover;
  width: 100%;
  border-radius: 100px 20px;
}
.about-one .sec-title {
  padding-bottom: 20px;
}
.about-one__top__text {
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 175%;
  margin-bottom: 24px;
  padding-bottom: 0;
  text-transform: capitalize;
  color: var(--wallox-text, #7E7C76);
}
.about-one__list {
  margin-bottom: 0;
}
.about-one__list__item {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 175%;
  margin-bottom: 0;
  padding-bottom: 0;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
  text-shadow: 0 0 0.1px currentColor;
}
.about-one__list__item i {
  color: var(--wallox-base, #DF9E42);
  font-size: 14px;
  margin-right: 10px;
}
.about-one__list__item + .about-one__list__item {
  margin-top: 10px;
}
.about-one__video {
  position: relative;
}
.about-one__video img {
  position: relative;
  object-fit: cover;
  width: 100%;
  border-radius: 30px;
}
.about-one__video-popup {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.about-one__video-popup a {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: var(--wallox-base, #DF9E42);
  background-color: var(--wallox-white, #fff);
  transition: all 0.4s ease-in-out;
}
.about-one__video-popup a:hover {
  background: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
}
.about-one__funfact {
  border-radius: 100px;
  background-color: var(--wallox-base, #DF9E42);
  display: flex;
  align-items: center;
  gap: 20px;
  min-height: 270px;
  bottom: 40px;
  left: 40px;
  transform: rotate(-180deg);
  position: absolute;
  writing-mode: vertical-rl;
}
.about-one__count {
  margin-bottom: 0;
  padding-bottom: 0;
  font-style: normal;
  font-weight: 800;
  font-size: 30px;
  line-height: 38px;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 91px;
  height: 113px;
  background: #FFFFFF;
  border-radius: 100px;
  margin: 0;
  text-shadow: 0 0 0.1px currentColor;
}
.about-one__funfact__text {
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 23px;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
  margin-bottom: 0;
  padding-bottom: 0;
  margin: 0;
}
.about-one__client {
  border-top: 1px solid var(--wallox-border-color, #E4DACC);
  margin-top: 30px;
  padding-top: 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (max-width: 1199px) and (min-width: 991px) {
  .about-one__client {
    flex-direction: column;
    gap: 20px;
    justify-content: start;
    align-items: start;
  }
}
@media (max-width: 575px) {
  .about-one__client {
    flex-direction: column;
    gap: 20px;
    justify-content: start;
    align-items: start;
  }
}
.about-one__client__item {
  display: flex;
  align-items: center;
  gap: 20px;
}
.about-one__client__thumb img {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  border: 3px solid var(--wallox-white, #fff);
  margin-left: -20px;
}
.about-one__client__thumb img:first-child {
  margin-left: 0;
}
.about-one__client__star {
  font-size: 16px;
  color: var(--wallox-base, #DF9E42);
  letter-spacing: 0.1px;
  margin-bottom: 0;
}
.about-one__client__text {
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 114%;
  margin-bottom: 0;
  padding-bottom: 0;
  text-transform: capitalize;
  color: var(--wallox-text, #7E7C76);
}
.about-one__shape {
  position: absolute;
  left: 0;
  bottom: 35%;
  animation: topToBottom 3s ease-in-out infinite;
  z-index: -1;
}
.about-one__shape-two {
  position: absolute;
  right: 0;
  bottom: 20%;
  animation: rotated 10s infinite linear;
  z-index: -1;
}

.about-two {
  padding: 120px 0px;
  position: relative;
  background-color: var(--wallox-gray, #F4EDE4);
  z-index: 1;
}
@media (max-width: 991px) {
  .about-two {
    padding: 100px 0px;
  }
}
@media (max-width: 767px) {
  .about-two {
    padding: 80px 0px;
  }
}
.about-two__bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-repeat: no-repeat;
  background-size: cover;
  opacity: 0.25;
  mix-blend-mode: multiply;
  z-index: -1;
}
.about-two__video-popup {
  position: absolute;
  top: 10%;
  right: 10%;
  z-index: 1;
}
.about-two__video-popup a {
  width: 83px;
  height: 83px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: var(--wallox-base, #DF9E42);
  background-color: var(--wallox-white, #fff);
  position: relative;
  transition: all 0.4s ease-in-out;
}
.about-two__video-popup a::before {
  content: "";
  position: absolute;
  top: -30%;
  left: -30%;
  width: 160%;
  height: 160%;
  border: 50px solid rgba(var(--wallox-white-rgb, 255, 255, 255), 0.6);
  border-radius: 50%;
  transform: scale(0.6);
  z-index: -1;
  animation: border-zooming 1.2s infinite linear;
  transition: all 0.4s ease-in-out;
}
.about-two__video-popup a::after {
  content: "";
  position: absolute;
  top: -30%;
  left: -30%;
  width: 160%;
  height: 160%;
  border: 50px solid rgba(var(--wallox-white-rgb, 255, 255, 255), 0.7);
  border-radius: 50%;
  transform: scale(0.6);
  z-index: -1;
  animation: border-zooming 1.2s infinite linear;
  animation-delay: 0.3s;
  transition: all 0.4s ease-in-out;
}
.about-two__video-popup a:hover {
  background: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
}
.about-two__video-popup a:hover::before, .about-two__video-popup a:hover::after {
  border: 50px solid rgba(var(--wallox-base-rgb, 223, 158, 66), 0.7);
}
.about-two__left {
  position: relative;
}
.about-two__thumb {
  position: relative;
}
.about-two__thumb__item img {
  border-radius: 1000px 1000px 0px 0px;
}
@media (max-width: 575px) {
  .about-two__thumb__item img {
    object-fit: cover;
    width: 100%;
  }
}
.about-two__thumb__item-two {
  position: absolute;
  bottom: 0;
  right: 0;
}
.about-two__thumb__item-two img {
  border: 10px solid var(--wallox-white, #fff);
  border-radius: 1000px 30px 0px 0px;
}
@media (max-width: 575px) {
  .about-two__thumb__item-two img {
    object-fit: cover;
    width: 100%;
  }
}
.about-two .sec-title {
  padding-bottom: 20px;
}
.about-two__top__text {
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 175%;
  margin-bottom: 20px;
  padding-bottom: 0;
  color: var(--wallox-text, #7E7C76);
}
.about-two__feature {
  display: flex;
  gap: 35px;
  align-items: center;
  padding-top: 20px;
  padding-bottom: 20px;
  border-top: 1px solid var(--wallox-border-color, #E4DACC);
  border-bottom: 1px solid var(--wallox-border-color, #E4DACC);
  margin-bottom: 30px;
}
@media (max-width: 575px) {
  .about-two__feature {
    flex-direction: column;
  }
}
.about-two__feature__title {
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 140%;
  text-transform: capitalize;
  margin-bottom: 10px;
  padding-bottom: 0;
  color: var(--wallox-text-dark, #2E2A20);
  text-shadow: 0 0 0.1px currentColor;
}
.about-two__feature__text {
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 175%;
  padding-bottom: 0;
  margin-bottom: 0;
  color: var(--wallox-text, #7E7C76);
}
.about-two__connect {
  display: flex;
  align-items: center;
  gap: 21px;
}
@media (max-width: 500px) {
  .about-two__connect {
    flex-direction: column;
    align-items: start;
    justify-content: start;
  }
}
.about-two__call {
  display: flex;
  align-items: center;
  gap: 15px;
}
.about-two__call__icon {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background-color: var(--wallox-base, #DF9E42);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  color: var(--wallox-white, #fff);
  position: relative;
  transition: all 0.4s ease-in-out;
}
.about-two__call__icon::after {
  content: "";
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: absolute;
  top: 0;
  left: 0;
  background: transparent;
  border: 0.5px solid var(--wallox-base, #DF9E42);
  transform: scale(1.2);
}
.about-two__call__subtitle {
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 140%;
  margin-bottom: 0;
  padding-bottom: 0;
  text-transform: capitalize;
  color: var(--wallox-text, #7E7C76);
  display: block;
}
.about-two__call__number {
  font-style: normal;
  font-weight: 800;
  font-size: 16px;
  line-height: 162%;
  margin-bottom: 0;
  padding-bottom: 0;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
  display: block;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.about-two__call__number:hover {
  background-size: 100% 1px;
}
.about-two__call:hover .about-two__call__icon {
  background: var(--wallox-text-dark, #2E2A20);
  color: var(--wallox-white, #fff);
}
.about-two__shape {
  position: absolute;
  left: 0;
  top: 10%;
  animation: top-bottom 4s ease-in-out infinite;
}
@media (max-width: 1199px) {
  .about-two__shape {
    display: none;
  }
}

.about-three {
  padding: 120px 0px;
  position: relative;
  background-color: var(--wallox-white, #fff);
  z-index: 1;
}
@media (max-width: 991px) {
  .about-three {
    padding: 100px 0px;
  }
}
@media (max-width: 767px) {
  .about-three {
    padding: 80px 0px;
  }
}
.about-three .sec-title {
  padding-bottom: 20px;
}
.about-three__top__text {
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 175%;
  margin-bottom: 0px;
  padding-bottom: 0;
}
.about-three__feature {
  margin-top: 35px;
  padding-top: 40px;
  border-top: 1px solid var(--wallox-border-color, #E4DACC);
  display: flex;
  gap: 20px;
  margin-bottom: 33px;
}
.about-three__feature__icon {
  max-width: 80px;
  width: 100%;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40px;
  color: var(--wallox-base, #DF9E42);
  background-color: var(--wallox-gray, #F4EDE4);
  border-radius: 6px;
  transition: all 0.4s ease-in-out;
}
.about-three__feature__title {
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 144%;
  margin-bottom: 3px;
  padding-bottom: 0;
  color: var(--wallox-text-dark, #2E2A20);
  text-transform: capitalize;
}
.about-three__feature__text {
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 162%;
  margin-bottom: 0;
  padding-bottom: 0;
  color: var(--wallox-text, #7E7C76);
}
.about-three__feature:hover .about-three__feature__icon {
  background-color: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
}
.about-three__left {
  position: relative;
}
.about-three__thumb {
  position: relative;
  z-index: 1;
  margin-left: 47px;
  margin-right: 30px;
}
@media (max-width: 991px) {
  .about-three__thumb {
    margin-right: 100px;
  }
}
@media (max-width: 767px) {
  .about-three__thumb {
    margin-right: 40px;
  }
}
@media (max-width: 575px) {
  .about-three__thumb {
    margin-right: 0px;
    margin-left: 0;
  }
}
.about-three__thumb img {
  border-radius: 10px;
}
@media (max-width: 1199px) {
  .about-three__thumb img {
    object-fit: cover;
    width: 100%;
  }
}
.about-three__thumb__small {
  position: absolute;
  right: 0;
  bottom: 40%;
  border-radius: 20px;
  border: 12px solid var(--wallox-white, #fff);
  filter: drop-shadow(0px 4px 40px rgba(0, 0, 0, 0.1));
}
.about-three__thumb__small img {
  border-radius: 20px;
}
@media (max-width: 991px) {
  .about-three__thumb__small {
    right: -70px;
  }
}
@media (max-width: 575px) {
  .about-three__thumb__small {
    right: -20px;
  }
}
.about-three__thumb__box {
  width: 80px;
  height: 226px;
  background-color: var(--wallox-gray, #F4EDE4);
  position: absolute;
  left: -30px;
  top: 30px;
  border-radius: 87.5px;
  z-index: -1;
  animation: topToBottom 3s ease-in-out infinite;
}
.about-three__thumb::after {
  content: "";
  position: absolute;
  width: 80px;
  height: 191px;
  left: -50px;
  top: 20px;
  z-index: -1;
  border: 3px solid var(--wallox-border-color, #E4DACC);
  border-radius: 87.5px;
  background-color: transparent;
  animation: topToBottom 3s ease-in-out infinite;
}
.about-three__thumb::before {
  content: "";
  position: absolute;
  width: 70px;
  height: 280px;
  right: 50px;
  bottom: 30px;
  z-index: -1;
  border: 3px solid var(--wallox-border-color, #E4DACC);
  border-radius: 87.5px;
  background-color: transparent;
  animation: topToBottom 3s ease-in-out infinite;
  animation-delay: 1s;
}
.about-three__funfact {
  position: absolute;
  bottom: 0;
  background: var(--wallox-base, #DF9E42);
  z-index: 1;
  padding: 30px;
  bottom: 50px;
}
@media (max-width: 575px) {
  .about-three__funfact {
    position: relative;
    margin-top: 30px;
    bottom: 0;
  }
}
.about-three__funfact__bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-repeat: no-repeat;
  background-size: cover;
  mix-blend-mode: multiply;
  z-index: -1;
}
.about-three__funfact__icon {
  font-size: 50px;
  text-align: center;
  color: var(--wallox-white, #fff);
  line-height: 0;
  margin-bottom: 22px;
}
.about-three__funfact__number {
  font-style: normal;
  font-weight: 800;
  font-size: 24px;
  line-height: 117%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--wallox-white, #fff);
  gap: 5px;
}
.about-three__funfact__text {
  margin-top: -5px;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 188%;
  color: var(--wallox-white, #fff);
  margin-bottom: 0;
  padding-bottom: 0;
  text-transform: capitalize;
  text-align: center;
}
.about-three__funfact::after {
  content: "";
  position: absolute;
  bottom: -29.555px;
  left: 0;
  height: 30px;
  width: 47.5555px;
  clip-path: polygon(100% 0, 100% 100%, 0 0);
  background-color: var(--wallox-text-dark, #2E2A20);
  z-index: -3;
}
@media (max-width: 575px) {
  .about-three__funfact::after {
    display: none;
  }
}
.about-three__bottom {
  display: flex;
  align-items: center;
  gap: 15px;
}
.about-three__bottom__text {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 100%;
  margin-bottom: 0;
  padding-bottom: 0;
  text-transform: uppercase;
  color: var(--wallox-base, #DF9E42);
  padding: 5px 10px;
  border-radius: 100px;
  border: 1px solid var(--wallox-base, #DF9E42);
}
.about-three__bottom__thumb {
  display: flex;
  align-items: center;
}
.about-three__bottom__thumb__item {
  border-radius: 50%;
  overflow: hidden;
  object-fit: cover;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: -20px;
}
.about-three__bottom__thumb__item:first-child {
  margin-left: 0;
}
.about-three__bottom__thumb__item--two {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: var(--wallox-white, #fff);
}
.about-three__bottom__thumb__item__funfact {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 100%;
  color: var(--wallox-text-dark, #2E2A20);
}
.about-three__shape {
  position: absolute;
  top: 15%;
  left: 0;
  animation: topToBottom 3s ease-in-out infinite;
}
@media (max-width: 991px) {
  .about-three__shape {
    display: none;
  }
}
.about-three__shape-two {
  position: absolute;
  bottom: 10%;
  right: 0;
  animation: rotated 18s ease-in-out infinite;
}
@media (max-width: 991px) {
  .about-three__shape-two {
    display: none;
  }
}

/*--------------------------------------------------------------
# Project
--------------------------------------------------------------*/
/** project-page**/
.project-page {
  padding: 120px 0px;
  position: relative;
}
@media (max-width: 991px) {
  .project-page {
    padding: 100px 0px;
  }
}
@media (max-width: 767px) {
  .project-page {
    padding: 80px 0px;
  }
}
.project-page--home {
  background-color: var(--wallox-gray, #F4EDE4);
}
.project-page__carousel {
  padding-bottom: 120px;
}
@media (max-width: 991px) {
  .project-page__carousel {
    padding-bottom: 100px;
  }
}
@media (max-width: 767px) {
  .project-page__carousel {
    padding-bottom: 80px;
  }
}
.project-page .project-carousel .owl-nav {
  display: none;
}
.project-page__item {
  position: relative;
  overflow: hidden;
}
.project-page__item__image {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
}
.project-page__item__image img {
  object-fit: cover;
  width: 100%;
}
.project-page__item__content {
  position: absolute;
  bottom: 30px;
  left: 30px;
  background: var(--wallox-white, #fff);
  border-radius: 20px;
  display: flex;
  gap: 100px;
  align-items: start;
  transform: translateY(200%);
  transition: all 0.6s ease-in-out;
}
.project-page__item__content__inner {
  padding: 25px 0px 25px 30px;
}
.project-page__item__content__subtitle {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  margin-bottom: 0;
  padding-bottom: 0;
  color: var(--wallox-base, #DF9E42);
  text-transform: uppercase;
  display: block;
  margin-bottom: 10px;
}
.project-page__item__content__title {
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 25px;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
  margin-bottom: 0;
  padding-bottom: 0;
}
.project-page__item__content__title a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.project-page__item__content__title a:hover {
  background-size: 100% 1px;
}
.project-page__item__content__link {
  padding: 35px 30px;
  background: var(--wallox-text-dark, #2E2A20);
  border-radius: 20px 20px 20px 80px;
  line-height: 0;
  font-size: 20px;
  color: var(--wallox-white, #fff);
  display: flex;
  justify-content: center;
  align-items: center;
}
.project-page__item__content__link i {
  line-height: 0;
  font-size: 600;
  transform: rotate(0);
  transition: all 0.4s ease-in-out;
}
.project-page__item__content__link:hover {
  background: var(--wallox-base, #DF9E42);
}
.project-page__item__content__link:hover i {
  transform: rotate(45deg);
}
.project-page__item:hover .project-page__item__content {
  transform: translateY(0%);
}
.project-page__top {
  padding-bottom: 55px;
}
.project-page__top .sec-title {
  padding-bottom: 0;
}
@media (max-width: 767px) {
  .project-page__top {
    padding-bottom: 35px;
  }
}
@media (max-width: 575px) {
  .project-page__top {
    padding-bottom: 30px;
  }
}
.project-page__top .project-page__top__nav {
  text-align: end;
  display: flex;
  justify-content: end;
  gap: 10px;
}
@media (max-width: 991px) {
  .project-page__top .project-page__top__nav {
    display: none;
  }
}
.project-page__top .project-page__top__nav .project-page__top__nav--left,
.project-page__top .project-page__top__nav .project-page__top__nav--right {
  border: none;
  outline: none;
  border-radius: 50%;
  margin: 0;
  padding: 0;
  background-color: transparent;
}
.project-page__top .project-page__top__nav .project-page__top__nav--left span,
.project-page__top .project-page__top__nav .project-page__top__nav--right span {
  outline: none;
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  font-size: 16px;
  transition: all 500ms ease;
  background-color: transparent;
  color: var(--wallox-base, #DF9E42);
  border: 1px solid var(--wallox-border-color, #E4DACC);
  font-weight: 900;
}
.project-page__top .project-page__top__nav .project-page__top__nav--left span:hover,
.project-page__top .project-page__top__nav .project-page__top__nav--right span:hover {
  background-color: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
  border-color: transparent;
}

/** project-card**/
.project-card {
  position: relative;
  overflow: hidden;
}
.project-card__thumb {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.4s ease-in-out;
  border: 5px solid transparent;
}
.project-card__thumb img {
  object-fit: cover;
  width: 100%;
  min-height: 470px;
  height: 100%;
}
@media (max-width: 1199px) {
  .project-card__thumb img {
    min-height: auto;
    height: auto;
  }
}
.project-card__content {
  position: absolute;
  bottom: 30px;
  right: 30px;
  left: 30px;
  background: var(--wallox-white, #fff);
  border-radius: 20px;
  display: flex;
  justify-content: space-between;
  align-items: start;
  transform: translateY(200%);
  transition: all 0.6s ease-in-out;
}
.project-card__content__inner {
  padding: 25px 0px 25px 30px;
}
.project-card__content__subtitle {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  margin-bottom: 0;
  padding-bottom: 0;
  color: var(--wallox-base, #DF9E42);
  text-transform: uppercase;
  display: block;
  margin-bottom: 10px;
}
.project-card__content__title {
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 25px;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
  margin-bottom: 0;
  padding-bottom: 0;
}
.project-card__content__title a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.project-card__content__title a:hover {
  background-size: 100% 1px;
}
.project-card__content__link {
  padding: 35px 30px;
  background: var(--wallox-base, #DF9E42);
  border-radius: 20px 20px 20px 80px;
  line-height: 0;
  font-size: 20px;
  color: var(--wallox-white, #fff);
  display: flex;
  justify-content: center;
  align-items: center;
}
.project-card__content__link i {
  line-height: 0;
  font-size: 600;
  transform: rotate(0);
  transition: all 0.4s ease-in-out;
}
.project-card__content__link:hover {
  background: var(--wallox-text-dark, #2E2A20);
}
.project-card__content__link:hover i {
  transform: rotate(45deg);
}
.project-card:hover .project-card__content {
  transform: translateY(0%);
}
.project-card:hover .project-card__thumb {
  border: 5px solid var(--wallox-base, #DF9E42);
}

.gutter-x-20 {
  --bs-gutter-x: 20px;
}

.gutter-y-20 {
  --bs-gutter-y: 20px;
}

/** project-details**/
.project-details {
  padding: 120px 0px;
  position: relative;
}
@media (max-width: 991px) {
  .project-details {
    padding: 100px 0px;
  }
}
@media (max-width: 767px) {
  .project-details {
    padding: 80px 0px;
  }
}
.project-details__top__thumb {
  border-radius: 20px;
  margin-bottom: 20px;
  overflow: hidden;
  max-height: 472px;
}
.project-details__top__thumb img {
  object-fit: cover;
  width: 100%;
  height: auto;
}
.project-details__category {
  margin-bottom: 60px;
  padding: 40px;
  background: var(--wallox-gray, #F4EDE4);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 120px;
  border-radius: 20px;
}
@media (max-width: 1199px) {
  .project-details__category {
    gap: 70px;
  }
}
@media (max-width: 991px) {
  .project-details__category {
    flex-wrap: wrap;
    gap: 30px 0;
  }
}
.project-details__category__item {
  position: relative;
}
.project-details__category__item:not(:first-of-type)::before {
  content: "";
  width: 1px;
  height: 100%;
  background-color: var(--wallox-border-color, #E4DACC);
  position: absolute;
  top: 0;
  left: -60px;
}
@media (max-width: 1199px) {
  .project-details__category__item:not(:first-of-type)::before {
    left: -30px;
  }
}
@media (max-width: 991px) {
  .project-details__category__item:not(:first-of-type)::before {
    display: none;
  }
}
@media (max-width: 991px) {
  .project-details__category__item {
    flex: 48%;
    width: 48%;
  }
}
@media (max-width: 575px) {
  .project-details__category__item {
    flex: 100%;
    width: 100%;
  }
}
.project-details__category__subtitle {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 100%;
  margin-bottom: 16px;
  padding-bottom: 0;
  color: var(--wallox-base, #DF9E42);
  text-transform: capitalize;
  display: block;
}
.project-details__category__title {
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 89%;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
  margin-bottom: -2px;
  padding-bottom: 0;
}
.project-details__title {
  font-style: normal;
  font-weight: 700;
  font-size: 30px;
  line-height: 120%;
  margin-bottom: 15px;
  padding-bottom: 0;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
}
.project-details__text {
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 188%;
  text-transform: capitalize;
  color: var(--wallox-text, #7E7C76);
  margin-bottom: -3px;
  padding-bottom: 0;
}
.project-details__text + .project-details__text {
  margin-top: 15px;
}
.project-details__info + .project-details__quality {
  margin-top: 50px;
}
.project-details__quality__thumb img {
  object-fit: cover;
  width: 100%;
  border-radius: 20px;
  height: auto;
}
.project-details__quality__list {
  margin-top: 28px;
}
.project-details__quality__item {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 175%;
  margin-bottom: -2px;
  padding-bottom: 0;
  color: var(--wallox-text-dark, #2E2A20);
  text-transform: capitalize;
  text-shadow: 0 0 0.1px currentColor;
}
.project-details__quality__item i {
  color: var(--wallox-base, #DF9E42);
  margin-right: 12px;
}
.project-details__quality__item + .project-details__quality__item {
  margin-top: 10px;
}
.project-details__quality + .project-details__management {
  margin-top: 50px;
}
.project-details__management__inner__box {
  margin-top: 20px;
  margin-bottom: 20px;
}
.project-details__management__thumb img {
  object-fit: cover;
  width: 100%;
  border-radius: 20px;
}
.project-details__management__funfact {
  border-radius: 20px;
  background: var(--wallox-gray, #F4EDE4);
  padding: 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (max-width: 575px) {
  .project-details__management__funfact {
    flex-direction: column;
    justify-content: start;
    align-items: start;
    gap: 30px;
  }
}
.project-details__management__funfact__item {
  font-style: normal;
  font-weight: 500;
  font-size: 20px;
  line-height: 120%;
  margin-bottom: 0;
  padding-bottom: 0;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
}
.project-details__management__funfact__item__inner {
  margin-bottom: 30px;
  background: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 220px;
  height: 220px;
  border-radius: 50%;
  border: 1px solid var(--wallox-base, #DF9E42);
}
@media (max-width: 1199px) {
  .project-details__management__funfact__item__inner {
    width: 150px;
    height: 150px;
  }
}
.project-details__management__funfact__item__inner .project-details__funfact-item {
  width: 180px;
  height: 180px;
  border-radius: 50%;
  background-color: var(--wallox-white, #fff);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 0;
  font-style: normal;
  font-weight: 700;
  font-size: 30px;
  line-height: 120%;
  color: var(--wallox-text-dark, #2E2A20);
  transition: all 0.4s ease-in-out;
}
@media (max-width: 1199px) {
  .project-details__management__funfact__item__inner .project-details__funfact-item {
    width: 120px;
    height: 120px;
  }
}
.project-details__management__funfact__item__inner:hover .project-details__funfact-item {
  background: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
}
.project-details__management__funfact__text {
  font-style: normal;
  font-weight: 500;
  font-size: 20px;
  line-height: 120%;
  text-transform: capitalize;
  margin-bottom: 0;
  padding-bottom: 0;
  text-align: center;
}

/** project-two**/
.project-two {
  padding: 120px 0px;
  position: relative;
  z-index: 1;
  background: var(--wallox-gray, #F4EDE4);
}
@media (max-width: 991px) {
  .project-two {
    padding: 100px 0px;
  }
}
@media (max-width: 767px) {
  .project-two {
    padding: 80px 0px;
  }
}
.project-two__top {
  padding-bottom: 55px;
}
.project-two__top .sec-title {
  padding-bottom: 0;
}
@media (max-width: 767px) {
  .project-two__top {
    padding-bottom: 35px;
  }
}
@media (max-width: 575px) {
  .project-two__top {
    padding-bottom: 30px;
  }
}
.project-two__top .project-two__nav {
  text-align: end;
  display: flex;
  justify-content: end;
  gap: 10px;
}
@media (max-width: 991px) {
  .project-two__top .project-two__nav {
    display: none;
  }
}
.project-two__top .project-two__nav .project-two__carousel__nav--left,
.project-two__top .project-two__nav .project-two__carousel__nav--right {
  border: none;
  outline: none;
  border-radius: 50%;
  margin: 0;
  padding: 0;
}
.project-two__top .project-two__nav .project-two__carousel__nav--left span,
.project-two__top .project-two__nav .project-two__carousel__nav--right span {
  border: none;
  outline: none;
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  font-size: 16px;
  color: var(--wallox-text, #7E7C76);
  transition: all 500ms ease;
  background-color: var(--wallox-border-color, #E4DACC);
  color: var(--wallox-text, #7E7C76);
  font-weight: 900;
}
.project-two__top .project-two__nav .project-two__carousel__nav--left span:hover,
.project-two__top .project-two__nav .project-two__carousel__nav--right span:hover {
  background-color: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
}
.project-two__top .project-two__nav .project-two__nav--left,
.project-two__top .project-two__nav .project-two__nav--right {
  border: none;
  outline: none;
  border-radius: 50%;
  margin: 0;
  padding: 0;
  background-color: transparent;
}
.project-two__top .project-two__nav .project-two__nav--left span,
.project-two__top .project-two__nav .project-two__nav--right span {
  outline: none;
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  font-size: 16px;
  transition: all 500ms ease;
  background-color: var(--wallox-white, #fff);
  color: var(--wallox-base, #DF9E42);
  border: 1px solid var(--wallox-white, #fff);
  font-weight: 900;
}
.project-two__top .project-two__nav .project-two__nav--left span:hover,
.project-two__top .project-two__nav .project-two__nav--right span:hover {
  background-color: transparent;
  color: var(--wallox-white, #fff);
  border: 1px solid rgba(var(--wallox-white-rgb, 255, 255, 255), 0.15);
}
.project-two--home {
  padding: 120px 0px 0;
}
.project-two--home::after {
  display: none;
}
@media (max-width: 991px) {
  .project-two--home {
    padding: 100px 0px 0;
  }
}
@media (max-width: 767px) {
  .project-two--home {
    padding: 80px 0px 0;
  }
}
.project-two--home .project-two__carousel--two .project-card__thumb {
  border-radius: 20px 20px 0px 0px;
}
.project-two__bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  mix-blend-mode: multiply;
  opacity: 0.15;
  z-index: -2;
}
.project-two::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  clip-path: polygon(85% 0, 100% 0, 100% 100%, 0% 100%);
  background-color: var(--wallox-base, #DF9E42);
}
.project-two__inner .container {
  max-width: 1600px;
  margin-right: -40px;
  margin-left: auto;
}
@media (max-width: 991px) {
  .project-two__inner .container {
    margin-right: 0px;
  }
}
.project-two .client-carousel {
  margin-top: 100px;
}
@media (max-width: 991px) {
  .project-two .client-carousel {
    margin-top: 20px;
  }
}
@media (max-width: 767px) {
  .project-two .client-carousel {
    margin-top: 0px;
  }
}
.project-two__carousel {
  position: relative;
}
.project-two__carousel .project-card__content {
  transform: translateY(0);
}
.project-two__carousel .project-card__thumb,
.project-two__carousel .project-card:hover .project-card__thumb {
  border: 0px solid transparent;
}
.project-two__carousel .project-card__content__link {
  background: var(--wallox-text-dark, #2E2A20);
}
.project-two__carousel .project-card__content__link:hover {
  background-color: var(--wallox-base, #DF9E42);
}
.project-two__cloth {
  position: absolute;
  bottom: 70px;
  left: 50px;
  animation: topToBottom 3s ease-in-out infinite;
}
@media (max-width: 1700px) {
  .project-two__cloth {
    display: none;
  }
}
.project-two__shape {
  position: absolute;
  top: 45%;
  right: 0;
  animation: topToBottom 3s ease-in-out infinite;
}
@media (max-width: 991px) {
  .project-two__shape {
    display: none;
  }
}
.project-two__shape-two {
  position: absolute;
  bottom: 10%;
  left: 0;
  animation: topToBottom 3s ease-in-out infinite;
  animation-delay: 1s;
  z-index: -1;
}
@media (max-width: 991px) {
  .project-two__shape-two {
    display: none;
  }
}

/** project-page**/
.project-three {
  padding: 220px 0px;
  position: relative;
  z-index: 1;
  background: var(--wallox-gray, #F4EDE4);
}
@media (max-width: 1199px) {
  .project-three {
    padding: 120px 0px;
  }
}
@media (max-width: 991px) {
  .project-three {
    padding: 100px 0px;
  }
}
@media (max-width: 767px) {
  .project-three {
    padding: 80px 0px;
  }
}
.project-three__bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  mix-blend-mode: multiply;
  opacity: 0.15;
  z-index: -1;
}
.project-three .sec-title {
  padding-bottom: 20px;
}
.project-three__top__text {
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 175%;
  margin-bottom: 0;
  padding-bottom: 0;
  text-transform: capitalize;
  color: var(--wallox-text, #7E7C76);
  margin-bottom: 30px;
}
.project-three__carousel__inner {
  background-color: var(--wallox-white, #fff);
  border-radius: 100px 0px 0px 100px;
  padding: 30px;
  margin-bottom: 60px;
}
.project-three__carousel__inner__item {
  margin-left: 200px;
  padding-left: 30px;
  border-left: 1px solid var(--wallox-border-color, #E4DACC);
}
@media (max-width: 1199px) {
  .project-three__carousel__inner__item {
    margin-left: 70px;
  }
}
@media (max-width: 575px) {
  .project-three__carousel__inner__item {
    margin-left: 50px;
    border-color: transparent;
  }
}
.project-three__carousel__inner__item .wallox-slick__counter {
  display: flex;
  align-items: center;
  position: absolute;
  left: -235px;
  top: 0;
  bottom: 0;
  margin: auto;
  font-size: 24px;
  font-weight: 500;
  color: var(--wallox-text-dark, #2E2A20);
}
@media (max-width: 1199px) {
  .project-three__carousel__inner__item .wallox-slick__counter {
    left: -100px;
  }
}
@media (max-width: 575px) {
  .project-three__carousel__inner__item .wallox-slick__counter {
    left: -85px;
  }
}
.project-three__carousel__inner__item .wallox-slick__counter__active {
  color: var(--wallox-base, #DF9E42);
  font-weight: 800;
}
.project-three__carousel__inner__item .wallox-slick__counter__border {
  color: var(--wallox-border-color, #E4DACC);
  font-weight: 100;
  margin: 0 5px 0 7px;
  position: relative;
  top: -2px;
}
.project-three__carousel__inner .owl-dots {
  display: none !important;
}
.project-three__carousel__inner .owl-nav {
  position: absolute;
  left: -80%;
}
@media (max-width: 1199px) {
  .project-three__carousel__inner .owl-nav {
    left: -40%;
  }
}
@media (max-width: 991px) {
  .project-three__carousel__inner .owl-nav {
    left: -20%;
  }
}
.project-three__carousel__inner .owl-nav button span {
  color: var(--wallox-base, #DF9E42) !important;
  background-color: var(--wallox-white, #fff) !important;
}
.project-three__carousel__inner .owl-nav button span:hover {
  color: var(--wallox-white, #fff) !important;
  background-color: var(--wallox-base, #DF9E42) !important;
}
.project-three__item__subtitle {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  margin-bottom: 10px;
  padding-bottom: 0;
  color: var(--wallox-base, #DF9E42);
  text-transform: uppercase;
  display: block;
}
.project-three__item__title {
  font-style: normal;
  font-weight: 700;
  font-size: 20px;
  line-height: 25px;
  text-transform: capitalize;
  margin-bottom: 10px;
  padding-bottom: 0;
}
.project-three__item__text {
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 175%;
  color: var(--wallox-text, #7E7C76);
  margin-bottom: -4px;
}
.project-three__thumb__carousel {
  position: absolute;
  width: calc(50% - 15px);
  right: 0px;
  top: 50%;
  transform: translateY(-50%);
}
@media (max-width: 1199px) {
  .project-three__thumb__carousel {
    position: relative;
    width: 100%;
    top: auto;
    transform: translateY(0%);
  }
}
.project-three__thumb__carousel .wallox-slick__counter {
  display: none;
}
.project-three__thumb__item img {
  border-radius: 20px 0 0 20px;
}
.project-three__element {
  position: absolute;
  top: 0%;
  left: 0;
  animation: topToBottom 4s ease-in-out infinite;
  z-index: -1;
}
.project-three__element-two {
  position: absolute;
  top: 0;
  right: 0;
  animation: topToBottom 4s ease-in-out infinite;
  animation-delay: 1s;
  z-index: -1;
}

/*--------------------------------------------------------------
# Services
--------------------------------------------------------------*/
.service-page {
  padding: 120px 0;
}
@media (max-width: 767px) {
  .service-page {
    padding: 100px 0;
  }
}
@media (max-width: 767px) {
  .service-page {
    padding: 80px 0;
  }
}

.service-card {
  position: relative;
  z-index: 1;
  overflow: hidden;
  border-radius: 20px;
  transition: all 0.4s ease-in-out;
}
.service-card__image {
  position: relative;
}
.service-card__image img {
  object-fit: cover;
  width: 100%;
  transform: scale(1);
  transition: all 0.4s ease-in-out;
  position: relative;
  z-index: 1;
}
.service-card__hover__box {
  background-color: rgba(var(--wallox-text-dark-rgb, 46, 42, 32), 0.7);
  width: 100%;
  top: 0;
  position: absolute;
  transform: scaleY(0);
  transition: all 0.5s ease 0s;
  height: 25%;
  z-index: 1;
}
.service-card__hover__box--1 {
  top: 0;
  transition-delay: 0.105s;
}
.service-card__hover__box--2 {
  top: 25%;
  transition-delay: 0.105s;
}
.service-card__hover__box--3 {
  top: 50%;
  transition-delay: 0.105s;
}
.service-card__hover__box--4 {
  top: 75%;
  transition-delay: 0s;
}
.service-card__content {
  background: transparent;
  position: absolute;
  bottom: 0;
  z-index: 1;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.service-card__title__box {
  background: var(--wallox-gray, #F4EDE4);
  padding: 25px 30px;
  width: 100%;
  transition: all 0.4s ease-in-out;
}
.service-card__title {
  font-style: normal;
  font-weight: 800;
  font-size: 24px;
  line-height: 30px;
  margin-bottom: 0;
  padding-bottom: 0;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
  transition: all 0.4s ease-in-out;
}
.service-card__title a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.service-card__title a:hover {
  background-size: 100% 1px;
}
.service-card__title a:hover {
  color: var(--wallox-base, #DF9E42);
}
.service-card__icon {
  background: var(--wallox-white, #fff);
  display: flex;
  min-height: 79px;
  justify-content: center;
  align-items: center;
  font-size: 40px;
  color: var(--wallox-base, #DF9E42);
  max-width: 91px;
  width: 100%;
  position: relative;
  margin-top: -158px;
  padding: 20px 15px;
  transition: all 0.4s ease-in-out;
}
.service-card:hover .service-card__icon {
  margin-top: 0px;
}
.service-card:hover .service-card__title__box {
  background: var(--wallox-white, #fff);
}
.service-card:hover .service-card__image img {
  transform: scale(1.1);
}
.service-card:hover .service-card__hover__box {
  opacity: 1;
  transform: scaleY(1);
}
.service-card:hover {
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
}

/****Service One****/
.service-one {
  position: relative;
  padding: 120px 0;
  background-color: var(--wallox-text-dark, #2E2A20);
  z-index: 1;
}
@media (max-width: 991px) {
  .service-one {
    padding: 100px 0;
  }
}
@media (max-width: 767px) {
  .service-one {
    padding: 80px 0;
  }
}
.service-one__bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  mix-blend-mode: overlay;
  opacity: 0.3;
  z-index: -1;
}
.service-one .sec-title__title {
  color: var(--wallox-white, #fff);
}
.service-one__item {
  overflow: hidden;
  border-radius: 185px;
  position: relative;
  max-height: 540px;
  min-height: 149px;
  height: 100%;
}
.service-one__item__thumb {
  position: relative;
}
.service-one__item__thumb img {
  border-radius: 185px;
  object-fit: cover;
  width: 100%;
  height: 100%;
}
.service-one__item__thumb::after {
  border-radius: 185px;
  width: 100%;
  height: 100%;
  content: "";
  position: absolute;
  bottom: 0;
  transition: all 0.4s ease-in-out;
  left: 0;
  background: linear-gradient(180deg, rgba(242, 238, 234, 0) 43.73%, #F2EEEA 82.3%);
}
.service-one__item__thumb::before {
  border-radius: 185px;
  left: 20px;
  right: 20px;
  position: absolute;
  bottom: 20px;
  content: "";
  top: 20px;
  background-image: url(../images/shapes/service-one-border-shape.png);
  background-repeat: no-repeat;
  background-size: contain;
  opacity: 0;
  transform: translateY(-100px);
  transition: opacity 500ms ease, transform 500ms ease;
}
.service-one__item__content {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  padding: 20px;
}
.service-one__item__content__inner {
  margin-left: auto;
  margin-right: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  align-items: center;
}
.service-one__item__title {
  font-style: normal;
  font-weight: 800;
  font-size: 24px;
  line-height: 30px;
  padding-bottom: 0;
  text-transform: capitalize;
  color: var(--wallox-text-dark, #2E2A20);
  margin-bottom: 25px;
  transition: all 0.2s ease;
}
.service-one__item__title a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.service-one__item__title a:hover {
  background-size: 100% 1px;
}
.service-one__item__btn {
  width: 70px;
  border-radius: 50%;
  height: 70px;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
  margin-right: auto;
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: var(--wallox-text-dark, #2E2A20);
  font-size: 20px;
}
.service-one__item__btn::before {
  background-color: var(--wallox-white, #fff);
}
.service-one__item__btn:hover {
  color: var(--wallox-base, #DF9E42);
  border: 1px solid var(--wallox-white, #fff);
}
.service-one__item:hover .service-one__item__btn {
  background: var(--wallox-white, #fff);
  color: var(--wallox-base, #DF9E42);
}
.service-one__item:hover .service-one__item__thumb::after {
  background: linear-gradient(180deg, rgba(223, 159, 67, 0) 43.73%, #DF9F43 82.3%);
}
.service-one__item:hover .service-one__item__thumb::before {
  opacity: 1;
  transform: translateY(0px);
}
.service-one__item:hover .service-one__item__title {
  color: var(--wallox-white, #fff);
}
.service-one__bottom {
  max-width: 718px;
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  border: 1px solid rgba(var(--wallox-white-rgb, 255, 255, 255), 0.1);
  border-radius: 100px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 11px 30px;
  gap: 60px;
}
.service-one__bottom__link p {
  font-style: normal;
  font-weight: 700;
  font-size: 16px;
  line-height: 20px;
  margin-bottom: 0;
  padding-bottom: 0;
  text-transform: capitalize;
  color: var(--wallox-white, #fff);
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.service-one__bottom__link p a {
  color: var(--wallox-base, #DF9E42);
}
.service-one__bottom__link p:hover {
  background-size: 100% 1px;
}
.service-one__bottom__call {
  display: flex;
  align-items: center;
  gap: 10px;
}
.service-one__bottom__call__icon {
  font-size: 40px;
  color: var(--wallox-white, #fff);
  line-height: 0;
}
.service-one__bottom__call__title {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  margin-bottom: 5px;
  padding-bottom: 0;
  display: block;
  color: var(--wallox-text, #7E7C76);
}
.service-one__bottom__call__number {
  font-style: normal;
  font-weight: 600;
  font-size: 24px;
  line-height: 30px;
  margin-bottom: 0;
  padding-bottom: 0;
  color: var(--wallox-white, #fff);
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.service-one__bottom__call__number:hover {
  background-size: 100% 1px;
}
.service-one__bottom__call__number:hover {
  color: var(--wallox-base, #DF9E42);
}
.service-one__bottom__nav {
  display: flex;
  align-items: center;
  gap: 10px;
}
.service-one__bottom__nav button {
  border: none;
  outline: none;
  border-radius: 50%;
  margin: 0;
  padding: 0;
}
.service-one__bottom__nav button span {
  border: none;
  outline: none;
  width: 50px;
  height: 50px;
  background-color: var(--wallox-gray, #F4EDE4);
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--wallox-text, #7E7C76);
  border-radius: 50%;
  font-size: 14px;
  color: var(--wallox-text, #7E7C76);
  transition: all 500ms ease;
}
.service-one__bottom__nav button span:hover {
  background-color: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
}
@media (max-width: 767px) {
  .service-one__bottom {
    flex-direction: column;
    justify-content: start;
    align-items: start;
    gap: 20px;
    padding-left: 70px;
  }
}
.service-one__middle {
  margin-bottom: 60px;
}
@media (max-width: 991px) {
  .service-one__middle {
    padding: 0px 15px;
  }
}
.service-one__carousel .owl-nav {
  position: absolute;
  bottom: 0;
  right: 40%;
}

/****Service Two****/
.service-two {
  padding: 120px 0 1px;
  position: relative;
  background-color: var(--wallox-text-dark, #2E2A20);
  z-index: 1;
}
@media (max-width: 767px) {
  .service-two {
    padding: 100px 0 1px;
  }
}
@media (max-width: 767px) {
  .service-two {
    padding: 80px 0 1px;
  }
}
.service-two__bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  opacity: 0.2;
  z-index: -1;
}
.service-two .sec-title__title {
  color: var(--wallox-white, #fff);
}
.service-two__inner {
  counter-reset: my-sec-counter;
}
.service-two__inner__top {
  padding-bottom: 55px;
}
.service-two__inner__top .sec-title {
  padding-bottom: 0;
}
@media (max-width: 767px) {
  .service-two__inner__top {
    padding-bottom: 35px;
  }
}
@media (max-width: 575px) {
  .service-two__inner__top {
    padding-bottom: 30px;
  }
}
.service-two__inner__top .service-two__inner__nav {
  text-align: end;
  display: flex;
  justify-content: end;
  gap: 10px;
}
@media (max-width: 991px) {
  .service-two__inner__top .service-two__inner__nav {
    display: none;
  }
}
.service-two__inner__top .service-two__inner__nav .service-two__inner__nav--left,
.service-two__inner__top .service-two__inner__nav .service-two__inner__nav--right {
  border: none;
  outline: none;
  border-radius: 50%;
  margin: 0;
  padding: 0;
  background: transparent;
}
.service-two__inner__top .service-two__inner__nav .service-two__inner__nav--left span,
.service-two__inner__top .service-two__inner__nav .service-two__inner__nav--right span {
  outline: none;
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  font-size: 14px;
  transition: all 500ms ease;
  background-color: transparent;
  color: var(--wallox-white, #fff);
  border: 1px solid rgba(var(--wallox-white-rgb, 255, 255, 255), 0.15);
  font-weight: 900;
}
.service-two__inner__top .service-two__inner__nav .service-two__inner__nav--left span:hover,
.service-two__inner__top .service-two__inner__nav .service-two__inner__nav--right span:hover {
  background-color: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
  border-color: var(--wallox-base, #DF9E42);
}
.service-two__carousel {
  position: relative;
}
@media (max-width: 991px) {
  .service-two__carousel {
    padding: 0px 15px;
  }
}
.service-two__item {
  height: 545px;
  background-color: transparent;
  width: 100%;
  position: relative;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 120px;
  z-index: 1;
}
.service-two__item::after {
  content: "";
  width: 100%;
  height: 1px;
  position: absolute;
  left: 0;
  background-color: rgba(255, 255, 255, 0.1);
  bottom: 0;
}
.service-two__item__bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  bottom: -120px;
  z-index: -2;
  opacity: 0;
  transition: all 0.4s ease-in-out;
  transform: scale(0.4);
}
.service-two__item__bg::after {
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  background: linear-gradient(180deg, rgba(46, 42, 32, 0) 51.1%, #2E2A20 100%);
  bottom: 0;
}
.service-two__item__content {
  position: absolute;
  bottom: 25px;
  left: 25px;
}
.service-two__item__number {
  background: var(--wallox-base, #DF9E42);
  display: inline-block;
  padding: 0px 14px;
  border-radius: 100px;
  margin-bottom: 15px;
  counter-increment: my-sec-counter;
}
.service-two__item__number::before {
  content: counters(my-sec-counter, ".", decimal-leading-zero);
  color: var(--wallox-text-dark, #2E2A20);
  font-style: normal;
  font-weight: 700;
  font-size: 16px;
  line-height: 20px;
  transition: all 0.4s ease-in-out;
}
.service-two__item__title {
  font-style: normal;
  font-weight: 800;
  font-size: 24px;
  line-height: 30px;
  margin-bottom: 0;
  padding-bottom: 0;
  color: var(--wallox-white, #fff);
  text-transform: capitalize;
}
.service-two__item__title a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.service-two__item__title a:hover {
  background-size: 100% 1px;
}
.service-two__item__title a:hover {
  color: var(--wallox-base, #DF9E42);
}
.service-two__item__link {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  color: var(--wallox-base, #DF9E42);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  position: absolute;
  top: 30px;
  left: 30px;
  opacity: 0;
  transition: all 0.4s ease-in-out;
}
.service-two__item:hover .service-two__item__bg {
  opacity: 1;
  transform: scale(1);
}
.service-two__item:hover .service-two__item__link {
  opacity: 1;
}
.service-two__item:hover .service-two__item__number::before {
  color: var(--wallox-white, #fff);
}

/****Service Three****/
.service-three {
  padding: 120px 0;
  position: relative;
  background-color: var(--wallox-text-dark, #2E2A20);
  z-index: 1;
  counter-reset: mainSliderTwoCount;
}
@media (max-width: 767px) {
  .service-three {
    padding: 100px 0;
  }
}
@media (max-width: 767px) {
  .service-three {
    padding: 80px 0;
  }
}
.service-three__bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  mix-blend-mode: multiply;
  opacity: 0.25;
  z-index: -1;
}
.service-three__top {
  margin-top: 50px;
  margin-bottom: 60px;
  padding-top: 40px;
  padding-bottom: 30px;
  border-top: 1px solid rgba(var(--wallox-border-color-rgb, 228, 218, 204), 0.15);
  border-bottom: 1px solid rgba(var(--wallox-border-color-rgb, 228, 218, 204), 0.15);
}
.service-three__top__btn {
  display: flex;
  justify-content: end;
}
@media (max-width: 991px) {
  .service-three__top__btn {
    justify-content: start;
  }
}
.service-three__top__btn a {
  border-color: var(--wallox-base, #DF9E42);
  color: var(--wallox-base, #DF9E42);
}
.service-three .sec-title {
  padding-bottom: 0;
}
@media (max-width: 991px) {
  .service-three .sec-title {
    padding-bottom: 20px;
  }
}
.service-three .sec-title__title {
  color: var(--wallox-white, #fff);
}
.service-three__text-slider {
  margin-top: -20px;
}
.service-three__text-slider__inner {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 10px;
  animation: textScrolling 25s linear infinite;
  animation-direction: reverse;
  will-change: transform;
  position: relative;
  white-space: nowrap;
}
.service-three__text-slider__inner:hover {
  animation-play-state: paused;
}
.service-three__text-slider__item {
  color: var(--wallox-white, #fff);
  display: flex;
  align-items: center;
  font-style: normal;
  font-weight: 800;
  font-size: 80px;
  line-height: 112%;
  text-transform: capitalize;
}
@media (max-width: 1199px) and (min-width: 992px) {
  .service-three__text-slider__item {
    font-size: 70px;
  }
}
@media (max-width: 767px) {
  .service-three__text-slider__item {
    font-size: 70px;
  }
}
.service-three__text-slider__item img {
  margin-right: 10px;
}
.service-three__text-slider__item:nth-child(odd) {
  color: transparent;
  -webkit-text-stroke: 1px rgba(255, 255, 255, 0.8);
}
.service-three__item {
  border-radius: 30px;
}
.service-three__item:hover .service-three__thumb img {
  transform: scale(1.1);
}
.service-three__thumb {
  -webkit-mask: url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300.000000 178.000000">  <g transform="translate(0.000000,178.000000) scale(0.100000,-0.100000)" stroke="none"> <path d="M95 1766 c-41 -18 -83 -69 -90 -109 -3 -18 -5 -375 -3 -794 3 -751 3 -762 24 -789 11 -15 33 -37 48 -48 27 -21 35 -21 816 -21 781 0 789 0 816 21 62 46 69 64 74 209 5 145 12 163 74 209 26 20 43 21 321 24 277 3 296 2 333 -17 66 -34 76 -58 83 -206 6 -145 17 -178 71 -217 29 -20 44 -23 135 -23 110 0 132 9 177 69 21 27 21 35 21 816 0 781 0 789 -21 816 -11 15 -33 37 -48 48 -27 21 -31 21 -1414 23 -1138 2 -1392 0 -1417 -11z"/> </g> </svg>');
  mask: url('data:image/svg+xml;utf8, <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300.000000 178.000000">  <g transform="translate(0.000000,178.000000) scale(0.100000,-0.100000)" stroke="none"> <path d="M95 1766 c-41 -18 -83 -69 -90 -109 -3 -18 -5 -375 -3 -794 3 -751 3 -762 24 -789 11 -15 33 -37 48 -48 27 -21 35 -21 816 -21 781 0 789 0 816 21 62 46 69 64 74 209 5 145 12 163 74 209 26 20 43 21 321 24 277 3 296 2 333 -17 66 -34 76 -58 83 -206 6 -145 17 -178 71 -217 29 -20 44 -23 135 -23 110 0 132 9 177 69 21 27 21 35 21 816 0 781 0 789 -21 816 -11 15 -33 37 -48 48 -27 21 -31 21 -1414 23 -1138 2 -1392 0 -1417 -11z"/> </g> </svg>');
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-position: top left;
  mask-position: top left;
  -webkit-mask-size: cover;
  mask-size: cover;
  position: relative;
  width: 100%;
  height: 100%;
}
.service-three__thumb img {
  object-fit: cover;
  width: 100%;
  transition: all 0.4s ease-in-out;
  transform: scale(1);
}
@media (max-width: 575px) {
  .service-three__thumb {
    mask-image: none;
  }
  .service-three__thumb img {
    border-radius: 30px;
  }
}
.service-three__thumb::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: linear-gradient(180deg, rgba(46, 42, 32, 0) 0%, #2E2A20 100%);
  border-radius: 30px;
}
.service-three__content {
  padding: 30px;
  position: relative;
  z-index: 1;
  margin-top: -135px;
  margin-right: 63px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (max-width: 1199px) and (min-width: 992px) {
  .service-three__content {
    margin-right: 30px;
  }
}
@media (max-width: 991px) and (min-width: 768px) {
  .service-three__content {
    margin-right: 95px;
  }
}
@media (max-width: 767px) {
  .service-three__content {
    margin-right: 45px;
  }
}
@media (max-width: 575px) {
  .service-three__content {
    margin-right: 0px;
  }
}
.service-three__content__title {
  font-style: normal;
  font-weight: 800;
  font-size: 24px;
  line-height: 30px;
  color: var(--wallox-white, #fff);
  text-transform: capitalize;
}
@media (max-width: 575px) {
  .service-three__content__title {
    font-size: 20px;
  }
}
.service-three__content__title a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.service-three__content__title a:hover {
  background-size: 100% 1px;
}
.service-three__content__right {
  margin-bottom: -45px;
}
@media (max-width: 575px) {
  .service-three__content__right {
    margin-bottom: auto;
  }
}
.service-three__content__right a {
  border-radius: 30px;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  border: 2px solid var(--wallox-white, #fff);
  padding: 27px 33px;
}
.service-three__content__right a::before {
  border-radius: 28px;
}
.service-three__content__right a:hover {
  border-color: transparent;
}
@media (max-width: 1199px) and (min-width: 991px) {
  .service-three__content__right a {
    padding: 15px 25px;
  }
}
@media (max-width: 575px) {
  .service-three__content__right a {
    margin-right: 30px;
  }
}
.service-three__content__number {
  counter-increment: mainSliderTwoCount;
  background: var(--wallox-base, #DF9E42);
  display: inline-block;
  padding: 0px 14px;
  border-radius: 100px;
  margin-bottom: 15px;
}
.service-three__content__number::before {
  content: counters(mainSliderTwoCount, ".", decimal-leading-zero);
  color: var(--wallox-white, #fff);
  font-style: normal;
  font-weight: 700;
  font-size: 16px;
  line-height: 20px;
}

/*--------------------------------------------------------------
# Service details
--------------------------------------------------------------*/
.service-details {
  padding: 120px 0;
  background-color: var(--wallox-white, #fff);
}
@media (max-width: 100px) {
  .service-details {
    padding: 100px 0;
  }
}
@media (max-width: 767px) {
  .service-details {
    padding: 80px 0;
  }
}
.service-details__thumbnail {
  margin-bottom: 20px;
}
.service-details__thumbnail img {
  object-fit: cover;
  width: 100%;
  border-radius: 20px;
}
.service-details__title {
  font-style: normal;
  font-weight: 700;
  font-size: 30px;
  line-height: 120%;
  margin-bottom: 15px;
  padding-bottom: 0;
  color: var(--wallox-text-dark, #2E2A20);
  letter-spacing: -0.9px;
  text-transform: capitalize;
}
.service-details__text {
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 188%;
  margin-bottom: 0;
  padding-bottom: 0;
  text-transform: capitalize;
  color: var(--wallox-text, #7E7C76);
}
.service-details__text + .service-details__title {
  margin-top: 33px;
}
.service-details__left {
  padding: 40px 0px 40px 40px;
}
.service-details__feature {
  background: var(--wallox-gray, #F4EDE4);
  position: relative;
  margin-top: 30px;
  margin-bottom: 30px;
  border-radius: 20px;
  overflow: hidden;
}
.service-details__feature + .service-details__text {
  margin-bottom: -9px;
}
.service-details__list {
  margin-bottom: 0;
}
.service-details__list__item {
  display: flex;
  align-items: center;
  gap: 10px;
}
.service-details__list__item:hover .service-details__list__icon {
  background: var(--wallox-base, #DF9E42);
  color: var(--wallox-white, #fff);
}
.service-details__list__item + .service-details__list__item {
  margin-top: 20px;
}
.service-details__list__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: var(--wallox-white, #fff);
  font-size: 16px;
  color: var(--wallox-base, #DF9E42);
  transition: all 0.4s ease-in-out;
}
.service-details__list__text {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 21px;
  margin-bottom: 0;
  padding-bottom: 0;
  color: var(--wallox-text-dark, #2E2A20);
  text-transform: capitalize;
}
.service-details__thumb img {
  object-fit: cover;
  width: 100%;
  clip-path: polygon(30% 0%, 100% 0, 100% 100%, 0% 100%);
}
@media (max-width: 991px) {
  .service-details__thumb img {
    clip-path: polygon(0% 0%, 100% 0, 100% 100%, 0% 100%);
  }
}
.service-details .accrodion {
  border: 1px solid var(--wallox-border-color, #E4DACC);
}
.service-details .faq-page__accordion .active .accrodion-title h4::after {
  width: 106%;
}
.service-details .faq-page__accordion {
  margin-top: 20px;
}

/*--------------------------------------------------------------
# Instagram
--------------------------------------------------------------*/
.instagram-one {
  position: relative;
  background-color: var(--wallox-white, #fff);
}
.instagram-one--three {
  position: relative;
}
.instagram-one .container-fluid {
  max-width: 1800px;
  margin-left: auto;
  margin-right: auto;
}
.instagram-one__item {
  overflow: hidden;
  border-radius: 20px;
  position: relative;
}
.instagram-one__item img {
  object-fit: cover;
  width: 100%;
}
.instagram-one__item__icon a {
  font-size: 30px;
  color: var(--wallox-base, #DF9E42);
  line-height: 1;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
  opacity: 0;
  transition: all 0.4s ease-in-out;
}
.instagram-one__item::before {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background: rgba(var(--wallox-text-dark-rgb, 46, 42, 32), 0.8);
  z-index: 1;
  border-radius: 20px;
  transform: translateX(-100%);
  transition: all 0.4s ease-in-out;
}
.instagram-one__item:hover::before {
  transform: translateX(0%);
}
.instagram-one__item:hover .instagram-one__item__icon a {
  opacity: 1;
}
.instagram-one__inner {
  margin-bottom: -150px;
  position: relative;
  z-index: 1;
}

.instagram-page {
  padding: 120px 0;
}
@media (max-width: 991px) {
  .instagram-page {
    padding: 100px 0;
  }
}
@media (max-width: 767px) {
  .instagram-page {
    padding: 80px 0;
  }
}

/*--------------------------------------------------------------
# Opening Time
--------------------------------------------------------------*/
.opening {
  position: relative;
  margin-bottom: -85px;
}
@media (max-width: 1199px) {
  .opening {
    margin-bottom: 0;
  }
}
.opening--home-three {
  margin-bottom: 0;
  padding-bottom: 120px;
}
@media (max-width: 767px) {
  .opening--home-three {
    padding-bottom: 80px;
  }
}
.opening__wrapper {
  background-color: var(--wallox-base, #DF9E42);
  position: relative;
  z-index: 2;
  padding: 39px 0;
}
@media (max-width: 767px) {
  .opening__wrapper {
    padding: 30px 20px;
    text-align: center;
  }
}
.opening__wrapper::after {
  position: absolute;
  left: 0;
  top: 0;
  width: 180px;
  height: 100%;
  content: "";
  opacity: 0.102;
  background-image: linear-gradient(90deg, rgba(var(--wallox-text-dark-rgb, 46, 42, 32), 0) 0%, rgba(var(--wallox-text-dark-rgb, 46, 42, 32), 1) 100%);
}
@media (max-width: 991px) {
  .opening__wrapper::after {
    display: none;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .opening__wrapper::after {
    width: 155px;
  }
}
.opening__icon {
  font-size: 65px;
  color: var(--wallox-white, #fff);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 14px 0 0;
  margin-right: -10px;
  position: relative;
  z-index: 2;
}
.opening__title {
  color: var(--wallox-white, #fff);
  text-transform: uppercase;
  font-size: 30px;
  font-weight: 700;
  margin: 27px -30px 0 33px;
  position: relative;
}
@media (max-width: 767px) {
  .opening__title {
    margin: 20px 0 0;
  }
}
.opening__info {
  font-size: 12px;
  line-height: 30px;
  color: var(--wallox-white, #fff);
  text-transform: uppercase;
  padding: 12px 0 20px;
  margin: 0 0 0 -14px;
}
@media (max-width: 991px) {
  .opening__info {
    margin: 0 0 0;
    text-align: center;
  }
}
@media (max-width: 767px) {
  .opening__info {
    margin: 0 0 0;
  }
}
.opening__info--last {
  margin-left: -45px;
  padding-left: 60px;
  border-left: 1px solid rgba(var(--wallox-white-rgb, 255, 255, 255), 0.2);
}
@media (max-width: 991px) {
  .opening__info--last {
    margin: 0 0 0;
    padding: 0;
    border: none;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .opening__info--last {
    margin-left: -30px;
    padding-left: 20px;
  }
}
.opening__info__text {
  display: block;
  font-size: 20px;
  text-transform: none;
}

/*--------------------------------------------------------------
# Boxed Home
--------------------------------------------------------------*/
body.boxed-wrapper {
  position: relative;
}
body.boxed-wrapper .page-wrapper {
  max-width: 1530px;
  margin-left: auto;
  margin-right: auto;
  background-color: var(--wallox-white, #fff);
  box-shadow: 0px 0px 100px 0px rgba(var(--wallox-text-dark-rgb, 46, 42, 32), 0.08);
}

/*--------------------------------------------------------------
# Work Process
--------------------------------------------------------------*/
.work-process-one {
  position: relative;
  counter-reset: count;
  padding: 120px 0 90px;
}
@media (max-width: 767px) {
  .work-process-one {
    padding: 80px 0 50px;
  }
}
.work-process-one .sec-title {
  text-align: center;
}
.work-process-one__border {
  width: 100%;
  height: 2px;
  background-color: var(--wallox-base, #DF9E42);
  top: 103px;
  position: relative;
}
@media (max-width: 767px) {
  .work-process-one__border {
    display: none;
  }
}
.work-process-one__border::after {
  position: absolute;
  left: 0;
  top: -5px;
  width: 11px;
  height: 11px;
  background-color: var(--wallox-text-dark, #2E2A20);
  border-radius: 50%;
  content: "";
}
.work-process-one__border::before {
  position: absolute;
  right: 0;
  top: -5px;
  width: 11px;
  height: 11px;
  background-color: var(--wallox-text-dark, #2E2A20);
  border-radius: 50%;
  content: "";
}
.work-process-one__gradiant-left {
  height: 100%;
  position: absolute;
  display: block;
  left: 0;
  top: 0;
  width: 5%;
  border-width: 1px;
  border-style: solid;
  border-image: linear-gradient(-45deg, rgba(var(--wallox-base-rgb, 223, 158, 66), 1) 0%, rgba(var(--wallox-text-dark-rgb, 46, 42, 32), 1) 100%) 1;
}
.work-process-one__gradiant-right {
  height: 100%;
  position: absolute;
  display: block;
  right: 0;
  top: 0;
  width: 5%;
  border-width: 1px;
  border-style: solid;
  border-image: linear-gradient(-45deg, rgba(var(--wallox-text-dark-rgb, 46, 42, 32), 1) 0%, rgba(var(--wallox-base-rgb, 223, 158, 66), 1) 100%) 1;
}
.work-process-one__item {
  position: relative;
  counter-increment: count;
  margin-bottom: 30px;
}
.work-process-one__item__thumb {
  display: inline-block;
  width: 202px;
  height: 202px;
  border: 2px solid var(--wallox-base, #DF9E42);
  background-color: var(--wallox-white, #fff);
  border-radius: 50%;
  padding: 5px;
  position: relative;
  margin-bottom: 30px;
}
.work-process-one__item__thumb-wrap {
  background-color: var(--wallox-text-dark, #2E2A20);
  position: relative;
  border-radius: 50%;
  overflow: hidden;
}
.work-process-one__item__thumb-wrap::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  display: block;
  width: 0;
  height: 0;
  background: rgba(var(--wallox-white-rgb, 255, 255, 255), 0.2);
  border-radius: 50%;
  transition: all 500ms linear;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  opacity: 0;
  z-index: 2;
}
.work-process-one__item__thumb-wrap img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  opacity: 0.3;
}
.work-process-one__item__thumb__number {
  width: 67px;
  height: 67px;
  background-color: var(--wallox-base, #DF9E42);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  color: var(--wallox-white, #fff);
  font-family: var(--wallox-heading-font, "Plus Jakarta Sans", serif);
  font-size: 30px;
  font-weight: 700;
  line-height: 1;
  position: absolute;
  right: -9px;
  top: 5px;
  transition: all 500ms linear;
  transition-delay: 0s;
  transition-delay: 0s;
  transition-delay: 0.1s;
  transform: scale(1);
}
.work-process-one__item__thumb__number::before {
  content: counters(count, ".", decimal-leading-zero);
  position: absolute;
  top: -6px;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}
.work-process-one__item__thumb__icon {
  width: 60px;
  height: 60px;
  font-size: 60px;
  color: var(--wallox-base, #DF9E42);
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  margin: auto;
}
.work-process-one__item:hover .work-process-one__item__thumb-wrap::after {
  -webkit-animation: zoom-hover 0.95s;
  animation: zoom-hover 0.95s;
}
.work-process-one__item:hover .work-process-one__item__thumb__number {
  transform: scale(0.95);
}
.work-process-one__item__content {
  position: relative;
  box-shadow: 0px 0px 60px 0px rgba(var(--wallox-text-dark-rgb, 46, 42, 32), 0.07);
  background-color: var(--wallox-white, #fff);
  border-radius: 100px;
  padding: 24px 20px 26px;
}
.work-process-one__item__content::after {
  position: absolute;
  left: 0;
  right: 0;
  top: -10px;
  content: "";
  margin: auto;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 25px 10px 25px;
  border-color: transparent transparent var(--wallox-white, #fff) transparent;
}
.work-process-one__item__title {
  font-size: 24px;
  font-weight: 700;
  text-transform: uppercase;
  margin: 0 0 5px;
}
.work-process-one__item__text {
  font-size: 15px;
  line-height: 26px;
  margin: 0;
}
/*# sourceMappingURL=wallox.css.map */
