<?php

namespace App\Http\Controllers;

use App\Models\People;
use Illuminate\Http\Request;

class PeopleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $people = People::select('full_name', 'image_url','image_alt', 'designation')->where('status', 'published')->get();
        return view('people.index')->with('people', $people);
    }


    /**
     * Display the specified resource.
     */
    // public function show(People $people)
    // {
    //     //
    // }

}
