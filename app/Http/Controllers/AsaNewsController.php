<?php

namespace App\Http\Controllers;

use App\Models\AsaNews;
use Illuminate\Http\Request;

class AsaNewsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(AsaNews $asaNews)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(AsaNews $asaNews)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, AsaNews $asaNews)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AsaNews $asaNews)
    {
        //
    }
}
