<?php

namespace App\Http\Controllers;

use App\Models\Expertise;
use App\Models\People;
use Illuminate\Http\Request;

class WelcomeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $expertises = Expertise::select('title', 'slug', 'content', 'icon', 'image_url', 'image_alt')->where('status', 'published')->orderBy('title', 'asc')->get();
        $people = People::select('full_name', 'image_url','image_alt', 'designation')->where('status', 'published')->get();
        return view('welcome')->with([
            'expertises' => $expertises,
            'people' => $people
        ]);
    }

}
