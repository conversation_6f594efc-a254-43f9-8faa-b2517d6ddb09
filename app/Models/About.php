<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Illuminate\Testing\Fluent\Concerns\Has;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class About extends Model
{
    use HasFactory;

    protected $fillable = [
        'page_title',
        'page_description',
        'page_keywords',
        'header',
        'subheader',
        'content',
        'image_url',
        'image_alt',
        'status'
    ];

    protected static function boot()
    {
        parent::boot();

        /** @var Model $model */
        static::updating(function ($model) {
            if ($model->isDirty('image_url') && ($model->getOriginal('image_url') !== null)) {
                Storage::disk('public')->delete($model->getOriginal('image_url'));
            }
        });
    }
}
