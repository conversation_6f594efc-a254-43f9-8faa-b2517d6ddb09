<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clients', function (Blueprint $table) {
            $table->id();
            $table->string('page_title')->nullable();
            $table->mediumText('page_description')->nullable();
            $table->mediumText('page_keywords')->nullable();
            $table->string('name', 150);
            $table->string('slug', 200);
            $table->text('description')->nullable();
            $table->string('logo_url', 100)->nullable();
            $table->string('image_alt')->nullable();
            $table->string('status', 20);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clients');
    }
};
