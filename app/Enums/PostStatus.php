<?php

namespace App\Enums;

use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;

enum PostStatus: string implements HasLabel, HasIcon, HasColor
{
    case PUBLISHED = 'published';
    case REVIEW = 'unpublished';
    case DRAFT = 'draft';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::PUBLISHED => 'Published',
            self::REVIEW => 'Unpublished',
            self::DRAFT => 'Draft',
        };
    }

    public function getColor(): ?string
    {
        return match ($this) {
            self::PUBLISHED => 'success',
            self::REVIEW => 'danger',
            self::DRAFT => 'secondary',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::PUBLISHED => 'heroicon-s-check-circle',
            self::REVIEW => 'heroicon-s-x-circle',
            self::DRAFT => 'heroicon-s-pencil',
        };
    }
}
