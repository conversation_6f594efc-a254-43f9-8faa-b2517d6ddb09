<?php

namespace App\Http\Controllers;

use App\Models\Contact;
use Illuminate\Http\Request;

class ContactController extends Controller
{
    /**
     * Display a listing of the resource.
     */
     public function index()
    {
        return view('contact.contact');
    }

    public function sendContact(Request $request)
    {
        $request->validate([
            'fullName' => ['required', 'string', 'max:100'],
            'email' => ['required', 'string', 'email', 'max:100'],
            'phone' => ['required', 'digits:10', 'numeric'],
            'subject' => ['required', 'string', 'max:255'],
            'message' => ['required', 'string'],
            // 'g-recaptcha-response' => ['required', new ReCaptcha]
        ]);

        Contact::create([
            'full_name' => stripslashes(strip_tags(trim($request->fullName))),
            'email' => stripslashes(strip_tags(trim($request->email))),
            'phone' => stripslashes(strip_tags(trim($request->phone))),
            'subject' => stripslashes(strip_tags(trim($request->subject))),
            'message' => stripslashes(strip_tags(trim($request->message))),
        ]);

        $details = [
            'fullname' => strip_tags($request->fullName),
            'email' => strip_tags($request->email),
            'phone' => strip_tags($request->phone),
            'subject' => strip_tags($request->subject),
            'message' => strip_tags($request->message),
        ];


        // Mail::to('<EMAIL>')->send(new ContactMail($details));

        return redirect()->back()->with(['success' => 'Your enquiry/feedback has been submitted, a representative will attend to you shortly']);
    }
}
