<?php

namespace App\Http\Controllers;

use App\Models\Solutions;
use Illuminate\Http\Request;

class SolutionsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Solutions $solutions)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Solutions $solutions)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Solutions $solutions)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Solutions $solutions)
    {
        //
    }
}
