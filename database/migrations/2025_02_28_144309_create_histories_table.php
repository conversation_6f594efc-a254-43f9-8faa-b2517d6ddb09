<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('histories', function (Blueprint $table) {
            $table->id();
            $table->string('page_title')->nullable();
            $table->mediumText('page_description')->nullable();
            $table->mediumText('page_keywords')->nullable();
            $table->string('header', 150);
            $table->string('subheader', 200)->nullable();
            $table->text('content');
            $table->string('image_url', 100);
            $table->string('image_alt')->nullable();
            $table->string('status', 20);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('histories');
    }
};
