<?php

namespace App\Http\Controllers;

use App\Models\JudicialInsight;
use Illuminate\Http\Request;

class JudicialInsightController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(JudicialInsight $judicialInsight)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(JudicialInsight $judicialInsight)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, JudicialInsight $judicialInsight)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(JudicialInsight $judicialInsight)
    {
        //
    }
}
